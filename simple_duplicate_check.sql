-- 简化版本：逐步调试重复档案查询

-- 方案1：最简单的重复检查（不使用递归CTE）
SELECT '=== 方案1：简单重复检查 ===' as method;

SELECT 
    parent_id as '父节点ID',
    archives_name as '档案名称',
    COUNT(*) as '重复次数',
    GROUP_CONCAT(archives_id) as '重复的档案ID'
FROM dgdoc_archives
WHERE is_sz = 0 
  AND archives_type = '1'
  AND parent_id IS NOT NULL 
  AND parent_id != ''
GROUP BY parent_id, archives_name
HAVING COUNT(*) > 1
ORDER BY COUNT(*) DESC;

-- 方案2：检查特定根节点下的重复
SELECT '=== 方案2：特定根节点下的重复检查 ===' as method;

-- 先找到根节点
WITH root_nodes AS (
    SELECT archives_id, archives_name
    FROM dgdoc_archives
    WHERE is_sz = 0 
      AND archives_type = '1'
      AND (parent_id IS NULL OR parent_id = '' OR LENGTH(TRIM(parent_id)) <= 3)
      AND archives_name IN (
        '基坑支护子分部工程验收资料抽查表',
        '桩基分项工程验收资料抽查表',
        '地基与基础分部工程验收资料抽查表',
        '建筑装饰装修分部工程验收资料抽查表',
        '屋面分部工程验收资料抽查表',
        '通风与空调分部工程验收资料抽查表',
        '建筑电气分部工程验收资料抽查表',
        '智能建筑分部工程验收资料抽查表'
      )
)

-- 然后检查这些根节点下的直接子节点是否有重复
SELECT 
    rn.archives_name as '根节点名称',
    da.archives_name as '子节点名称',
    COUNT(*) as '重复次数',
    GROUP_CONCAT(da.archives_id) as '重复的档案ID'
FROM root_nodes rn
JOIN dgdoc_archives da ON da.parent_id = rn.archives_id
WHERE da.is_sz = 0 AND da.archives_type = '1'
GROUP BY rn.archives_id, rn.archives_name, da.archives_name
HAVING COUNT(*) > 1
ORDER BY rn.archives_name, COUNT(*) DESC;

-- 方案3：多层级重复检查（使用递归CTE，但更简化）
SELECT '=== 方案3：多层级重复检查 ===' as method;

WITH RECURSIVE archive_hierarchy AS (
    -- 根节点
    SELECT 
        archives_id,
        parent_id,
        archives_name,
        archives_id as root_id,
        archives_name as root_name,
        1 as level
    FROM dgdoc_archives
    WHERE is_sz = 0 
      AND archives_type = '1'
      AND (parent_id IS NULL OR parent_id = '' OR LENGTH(TRIM(parent_id)) <= 3)
      AND archives_name IN (
        '基坑支护子分部工程验收资料抽查表',
        '桩基分项工程验收资料抽查表',
        '地基与基础分部工程验收资料抽查表',
        '建筑装饰装修分部工程验收资料抽查表',
        '屋面分部工程验收资料抽查表',
        '通风与空调分部工程验收资料抽查表',
        '建筑电气分部工程验收资料抽查表',
        '智能建筑分部工程验收资料抽查表'
      )
    
    UNION ALL
    
    -- 子节点
    SELECT 
        da.archives_id,
        da.parent_id,
        da.archives_name,
        ah.root_id,
        ah.root_name,
        ah.level + 1
    FROM dgdoc_archives da
    JOIN archive_hierarchy ah ON da.parent_id = ah.archives_id
    WHERE da.is_sz = 0 
      AND da.archives_type = '1'
      AND ah.level < 5  -- 限制递归深度
)

-- 查找同一根节点下的重复档案名称
SELECT 
    root_name as '根节点名称',
    archives_name as '重复的档案名称',
    COUNT(*) as '重复次数',
    GROUP_CONCAT(archives_id) as '重复的档案ID',
    GROUP_CONCAT(DISTINCT level) as '出现的层级'
FROM archive_hierarchy
WHERE level > 1  -- 排除根节点本身
GROUP BY root_id, root_name, archives_name
HAVING COUNT(*) > 1
ORDER BY root_name, COUNT(*) DESC;

-- 方案4：检查数据完整性
SELECT '=== 数据完整性检查 ===' as method;

-- 检查是否有数据
SELECT 
    '总档案数' as item,
    COUNT(*) as count
FROM dgdoc_archives
WHERE is_sz = 0 AND archives_type = '1'

UNION ALL

-- 检查根节点数量
SELECT 
    '根节点数（parent_id为空或长度<=3）' as item,
    COUNT(*) as count
FROM dgdoc_archives
WHERE is_sz = 0 
  AND archives_type = '1'
  AND (parent_id IS NULL OR parent_id = '' OR LENGTH(TRIM(parent_id)) <= 3)

UNION ALL

-- 检查目标根节点数量
SELECT 
    '目标根节点数' as item,
    COUNT(*) as count
FROM dgdoc_archives
WHERE is_sz = 0 
  AND archives_type = '1'
  AND (parent_id IS NULL OR parent_id = '' OR LENGTH(TRIM(parent_id)) <= 3)
  AND archives_name IN (
    '基坑支护子分部工程验收资料抽查表',
    '桩基分项工程验收资料抽查表',
    '地基与基础分部工程验收资料抽查表',
    '建筑装饰装修分部工程验收资料抽查表',
    '屋面分部工程验收资料抽查表',
    '通风与空调分部工程验收资料抽查表',
    '建筑电气分部工程验收资料抽查表',
    '智能建筑分部工程验收资料抽查表'
  );
