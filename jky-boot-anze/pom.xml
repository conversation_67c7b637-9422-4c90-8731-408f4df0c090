<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.jky.boot</groupId>
        <artifactId>jky-boot-parent</artifactId>
        <version>3.1</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>

    <artifactId>jky-boot-anze</artifactId>
    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.jky.boot</groupId>
            <artifactId>jky-boot-base-core</artifactId>
        </dependency>
        <!--shiro-->
        <dependency>
            <groupId>org.apache.shiro</groupId>
            <artifactId>shiro-spring-boot-starter</artifactId>
            <version>${shiro.version}</version>
        </dependency>

        <dependency>
            <groupId>com.jky.modules.estar</groupId>
            <artifactId>jky-module-estar</artifactId>
            <version>1.0.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.dtflys.forest</groupId>
            <artifactId>forest-spring-boot-starter</artifactId>
            <version>1.5.33</version>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.8.28</version>
        </dependency>
        <dependency>
            <groupId>com.jky.boot</groupId>
            <artifactId>jky-boot-dgdoc</artifactId>
            <version>3.1</version>
        </dependency>
    </dependencies>

</project>