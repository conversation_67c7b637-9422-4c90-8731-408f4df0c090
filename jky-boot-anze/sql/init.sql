create table dganze_insurance
(
    insurance_id bigint primary key not null,
    serial_no    varchar(128) default null comment '保单编号',
    org_id       varchar(64)  default null comment '保险公司id',
    project_name varchar(128) default null comment '项目名称',
    insurance_money double comment '保费金额（万元）',
    service_money double comment '服务金额（万元）',
    max_money double comment '保额大小(万元）',
    start_date   timestamp comment '保单有效期 开始',
    end_date     timestamp comment '保单有效期 结束',
    project_id   VARCHAR(64) COMMENT '项目id',
    create_by    VARCHAR(255) COMMENT '创建者',
    update_by    VARCHAR(255) COMMENT '更新者',
    create_time  TIMESTAMP    DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time  TIMESTAMP    DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX        idx_org_id (org_id),
    INDEX        idx_project_id (project_id)
) comment '保单表';

create table dganze_insurance_file
(
    insurance_file_id bigint primary key not null,
    insurance_id      bigint             not null comment 'dganze_insurance主键',
    file_path         text        default null comment '文件地址',
    org_id            varchar(64) default null comment '保险公司id',
    project_id        VARCHAR(64) COMMENT '项目id',
    create_by         VARCHAR(255) COMMENT '创建者',
    update_by         VARCHAR(255) COMMENT '更新者',
    create_time       TIMESTAMP   DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time       TIMESTAMP   DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX             idx_org_id (org_id),
    INDEX             idx_project_id (project_id)
) comment '保单附件';

create table dganze_insurance_org
(
    insurance_org_id bigint primary key not null,
    project_id       varchar(64)        not null comment '项目id',
    org_id           varchar(64)        not null comment '机构id',
    org_name         varchar(32) comment '机构名称',
    org_contract     varchar(12) comment '机构联系人',
    org_address      varchar(256) comment '机构地址',
    org_phone        varchar(256) comment '机构联系电话',
    create_by        VARCHAR(255) COMMENT '创建者',
    update_by        VARCHAR(255) COMMENT '更新者',
    create_time      TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time      TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY unique_project_org (project_id, org_name),
    INDEX            idx_org_id (org_id),
    INDEX            idx_project_id (project_id)
) comment '保单机构';

-- 创建服务记录表
CREATE TABLE dganze_insurance_service_record
(
    id                      BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    project_id              VARCHAR(64)  default NULL COMMENT '项目ID',
    project_name            varchar(128) default null comment '项目名称',
    org_id                  varchar(64)  default null comment '服务机构id',
    service_type            int       NOT NULL COMMENT '服务类型（0：技术咨询，1：应急演练，2：安全培训，3：现场巡查）',
    service_time            TIMESTAMP NOT NULL COMMENT '服务时间',
    entry_exit_photo        text COMMENT '出入场照片（存储图片路径）',
    scene_photo             text COMMENT '现场照片（存储图片路径）',
    location                VARCHAR(255) COMMENT '定位信息',
    service_personnel       VARCHAR(255) COMMENT '服务人员',
    personnel_qualification text COMMENT '人员资质（存储图片路径）',
    service_result          text COMMENT '服务结果（存储图片路径）',
    audit_state             int          DEFAULT 0 COMMENT '审核状态（0：审核中，1：审核通过，2：驳回）',
    create_by               VARCHAR(255) COMMENT '创建者',
    update_by               VARCHAR(255) COMMENT '更新者',
    created_time            TIMESTAMP    DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time            TIMESTAMP    DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX                   idx_org_id (org_id),
    INDEX                   idx_project_id (project_id)
) comment '服务记录表';

ALTER TABLE `sys_depart`
    ADD COLUMN `town_ship_id` VARCHAR(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci COMMENT '镇街ID'

-- 创建机构专家关联表
CREATE TABLE dganze_org_expert
(
    id          BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    org_id      VARCHAR(64) NOT NULL COMMENT '机构ID',
    expert_id   VARCHAR(64) NOT NULL COMMENT '专家ID',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    UNIQUE KEY uniq_org_expert (org_id, expert_id),
    INDEX       idx_org_id (org_id)
) COMMENT='机构和专家关联表';


create table dganze_insurance_service_record_service_result
(
    id        BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    result_id bigint not null,
    file_path text default null,
    INDEX idx_result_id (result_id)
) comment '服务记录（服务成功）表';

alter table sys_depart add column file_path text default null comment '营业资质文件';


create table dganze_insurance_event_record
(
    id             int auto_increment primary key comment '主键',
    record_content varchar(64) default null comment '记录内容',
    content_id     varchar(64) default null comment '记录相关的业务ID',
    project_id     varchar(64) default null comment '项目ID',
    record_title   varchar(512) default null comment '记录标题',
    record_status  varchar(5)  default null comment '业务的状态',
    record_type    int4        default null comment '业务类型 0:服务记录 1：项目记录（保单） 2：项目变更（选择服务机构）',
    create_time    timestamp   default now() comment '创建时间',
    org_id         VARCHAR(64) NOT NULL COMMENT '机构ID',
    user_id        VARCHAR(64) NOT NULL COMMENT '用户ID',
    INDEX idx_org_id (org_id)
) comment '项目记录变更表';