CREATE TABLE dganze_service_record_file
(
    id          BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    record_id   VARCHAR(64) NOT NULL COMMENT '服务记录表ID',
    project_id  varchar(64) not null comment '项目表ID',
    file_path   TEXT      DEFAULT NULL COMMENT '文件路径',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间'
) COMMENT='服务记录表管理的文件';