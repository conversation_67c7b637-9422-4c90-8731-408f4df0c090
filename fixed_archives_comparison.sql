-- 修复后的新旧档案对比查询
-- 目标：查找相同根节点下，有没有名字相同的子节点

-- 第一步：先运行这个调试查询，确认数据存在
/*
SELECT '=== 检查新档案根节点数据 ===' as debug_step;
SELECT archives_id, parent_id, archives_name, LENGTH(parent_id) as parent_len 
FROM dgdoc_archives 
WHERE is_sz = 0 AND archives_type = '1' AND LENGTH(parent_id) = 1
AND archives_name IN (
    '基坑支护子分部工程验收资料抽查表',
    '桩基分项工程验收资料抽查表',
    '地基与基础分部工程验收资料抽查表'
)
LIMIT 5;

SELECT '=== 检查旧档案根节点数据 ===' as debug_step;
SELECT archives_id, parent_id, archives_name, LENGTH(parent_id) as parent_len 
FROM dgdoc_archives 
WHERE is_sz = 1 AND archives_type = '1' AND LENGTH(parent_id) = 1
LIMIT 5;
*/

WITH RECURSIVE
-- 新档案树结构（从根节点开始递归）
new_tree AS (
    -- 基础查询：根节点
    SELECT
        da1.archives_id,
        da1.parent_id,
        da1.archives_name,
        da1.archives_type,
        da1.is_sz,
        1 AS level,
        CAST(da1.archives_name AS CHAR(1000)) AS path,
        CAST(da1.archives_id AS CHAR(1000)) AS id_path,
        da1.archives_name AS root_name,
        da1.archives_id AS root_id
    FROM
        dgdoc_archives da1
    WHERE
        LENGTH(da1.parent_id) = 1  -- 根节点parent_id长度为1
      AND da1.is_sz = 0
      AND da1.archives_type = '1'
      AND da1.archives_name IN (
                                '基坑支护子分部工程验收资料抽查表',
                                '桩基分项工程验收资料抽查表',
                                '地基与基础分部工程验收资料抽查表',
                                '建筑装饰装修分部工程验收资料抽查表',
                                '屋面分部工程验收资料抽查表',
                                '通风与空调分部工程验收资料抽查表',
                                '建筑电气分部工程验收资料抽查表',
                                '智能建筑分部工程验收资料抽查表'
        )

    UNION ALL

    -- 递归查询：子节点
    SELECT
        da2.archives_id,
        da2.parent_id,
        da2.archives_name,
        da2.archives_type,
        da2.is_sz,
        nt.level + 1,
        CONCAT(nt.path, ' -> ', da2.archives_name),
        CONCAT(nt.id_path, ',', da2.archives_id),
        nt.root_name,
        nt.root_id
    FROM
        dgdoc_archives da2
    INNER JOIN new_tree nt ON da2.parent_id = nt.archives_id
    WHERE
        da2.is_sz = 0
      AND da2.archives_type = '1'
      AND nt.level < 10  -- 防止无限递归
),

-- 旧档案树结构（统一使用LENGTH = 1）
old_tree AS (
    -- 基础查询：根节点
    SELECT
        da2.archives_id,
        da2.parent_id,
        da2.archives_name,
        da2.archives_type,
        da2.is_sz,
        1 AS level,
        CAST(da2.archives_name AS CHAR(1000)) AS path,
        CAST(da2.archives_id AS CHAR(1000)) AS id_path,
        da2.archives_name AS root_name,
        da2.archives_id AS root_id
    FROM
        dgdoc_archives da2
    WHERE
        LENGTH(da2.parent_id) = 1  -- 统一使用LENGTH = 1
      AND da2.is_sz = 1
      AND da2.archives_type = '1'

    UNION ALL

    -- 递归查询：子节点
    SELECT
        da3.archives_id,
        da3.parent_id,
        da3.archives_name,
        da3.archives_type,
        da3.is_sz,
        ot.level + 1,
        CONCAT(ot.path, ' -> ', da3.archives_name),
        CONCAT(ot.id_path, ',', da3.archives_id),
        ot.root_name,
        ot.root_id
    FROM
        dgdoc_archives da3
    INNER JOIN old_tree ot ON da3.parent_id = ot.archives_id
    WHERE
        da3.is_sz = 1
      AND da3.archives_type = '1'
      AND ot.level < 10  -- 防止无限递归
),

-- 查找新档案中的重复名称（同一根节点下）
new_duplicates AS (
    SELECT
        root_id,
        root_name,
        archives_name,
        COUNT(*) AS duplicate_count,
        GROUP_CONCAT(archives_id) AS duplicate_ids,
        GROUP_CONCAT(path SEPARATOR ' | ') AS duplicate_paths
    FROM new_tree
    WHERE level > 1  -- 排除根节点
    GROUP BY root_id, root_name, archives_name
    HAVING COUNT(*) > 1
)

-- 最终结果：显示重复的档案及其在旧档案中的对应情况
SELECT
    nd.root_name AS '新档案根节点',
    nd.archives_name AS '重复的档案名称',
    nd.duplicate_count AS '新档案中重复次数',
    nd.duplicate_paths AS '新档案中的路径',
    CASE 
        WHEN ot.archives_name IS NOT NULL THEN '是'
        ELSE '否'
    END AS '旧档案中是否存在',
    COALESCE(
        GROUP_CONCAT(DISTINCT ot.path SEPARATOR ' | '),
        '不存在'
    ) AS '旧档案中的路径'
FROM new_duplicates nd
LEFT JOIN old_tree ot ON nd.archives_name = ot.archives_name AND ot.level > 1
GROUP BY nd.root_id, nd.root_name, nd.archives_name, nd.duplicate_count, nd.duplicate_paths
ORDER BY nd.root_name, nd.duplicate_count DESC;

-- 额外查询：显示所有找到的根节点（用于调试）
/*
SELECT '=== 找到的新档案根节点 ===' as info;
SELECT DISTINCT root_name, root_id FROM new_tree WHERE level = 1;

SELECT '=== 找到的旧档案根节点 ===' as info;  
SELECT DISTINCT root_name, root_id FROM old_tree WHERE level = 1 LIMIT 10;
*/
