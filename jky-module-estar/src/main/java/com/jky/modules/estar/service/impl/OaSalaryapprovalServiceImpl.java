package com.jky.modules.estar.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jky.modules.estar.entity.OaSalaryapproval;
import com.jky.modules.estar.mapper.OaSalaryapprovalMapper;
import com.jky.modules.estar.service.IOaSalaryapprovalService;
import com.jky.modules.estar.vo.ProcessUpdateVo;

/**
 * @Description: 薪资审批表
 * @Author: jky
 * @Date:   2022-03-03
 * @Version: V1.0
 */
@Service
public class OaSalaryapprovalServiceImpl extends ServiceImpl<OaSalaryapprovalMapper, OaSalaryapproval> implements IOaSalaryapprovalService {
   
	@Autowired
	OaSalaryapprovalMapper oaSalaryapprovalMapper;
	
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProcessStatus(ProcessUpdateVo processUpdateVo)
    {
	    oaSalaryapprovalMapper.updateProcessStatus(processUpdateVo);
    }
    
    @Override
    public OaSalaryapproval getByInstanceId(String processInstanceId)
    {
	    return oaSalaryapprovalMapper.getByInstanceId(processInstanceId);
   }
}
