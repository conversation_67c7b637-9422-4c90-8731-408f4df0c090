package com.jky.modules.estar.oa.util;

import com.alibaba.fastjson.JSONArray;

import org.apache.commons.lang3.StringUtils;
import org.quartz.CronExpression;
import org.quartz.TriggerUtils;
import org.quartz.impl.triggers.CronTriggerImpl;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
*
* <AUTHOR>
* @version V3.1.0
* @copyright 引迈信息技术有限公司
* @date 2021/3/16 10:47
*/
public class DateUtil {
   //============================借助Calendar类获取今天、昨天、本周、上周、本年及特定时间的开始时间和结束时间（返回类型为date类型）========================

   /**
    * 获取当天开始时间
    *
    * @return
    */
   public static Date getDayBegin() {
       Calendar cal = Calendar.getInstance();
       //0点
       cal.set(Calendar.HOUR_OF_DAY, 0);
       // 0分
       cal.set(Calendar.MINUTE, 0);
       //0秒
       cal.set(Calendar.SECOND, 0);
       //0毫秒
       cal.set(Calendar.MILLISECOND, 0);
       return cal.getTime();
   }

   /**
    * 获取当前时间
    */
   public static String getNow() {
       LocalDateTime ldt1 = LocalDateTime.now();
       DateTimeFormatter dtf1 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
       String temp = dtf1.format(ldt1);
       return temp;
   }

   /**
    * 获取当前时间
    */
   public static String getNow(String zone) {
       LocalDateTime ldt1 = LocalDateTime.now(ZoneId.of(zone));
       DateTimeFormatter dtf1 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
       String temp = dtf1.format(ldt1);
       return temp;
   }


   /**
    * 获取当前时间
    */
   public static String getmmNow() {
       LocalDateTime ldt1 = LocalDateTime.now();
       DateTimeFormatter dtf1 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
       String temp = dtf1.format(ldt1);
       return temp;
   }


   /**
    * 获取当前时间
    */
   public static Date getNowDate() {
       Date date = new Date();
       return date;
   }

   /**
    * 获取当天结束时间
    *
    * @return
    */
   public static Date getDayEnd() {
       Calendar cal = Calendar.getInstance();
       //23点
       cal.set(Calendar.HOUR_OF_DAY, 23);
       //59分
       cal.set(Calendar.MINUTE, 59);
       //59秒
       cal.set(Calendar.SECOND, 59);
       return cal.getTime();
   }

   /**
    * 获取昨天开始时间
    *
    * @return
    */
   public static Date getBeginDayOfYesterday() {
       Calendar cal = Calendar.getInstance();
       //当天开始时间
       cal.setTime(getDayBegin());
       //当天月份天数减1
       cal.add(Calendar.DAY_OF_MONTH, -1);
       return cal.getTime();
   }

   /**
    * 获取昨天结束时间
    *
    * @return
    */
   public static Date getEndDayOfYesterday() {
       Calendar cal = Calendar.getInstance();
       //当天结束时间
       cal.setTime(getDayEnd());
       //当天月份天数减1
       cal.add(Calendar.DAY_OF_MONTH, -1);
       return cal.getTime();
   }

   /**
    * 获取明天开始时间
    *
    * @return
    */
   public static Date getBeginDayOfTomorrow() {
       Calendar cal = Calendar.getInstance();
       //当天开始时间
       cal.setTime(getDayBegin());
       //当天月份天数加1
       cal.add(Calendar.DAY_OF_MONTH, 1);
       return cal.getTime();
   }

   /**
    * 获取明天结束时间
    *
    * @return
    */
   public static Date getEndDayOfTomorrow() {
       Calendar cal = Calendar.getInstance();
       //当天结束时间
       cal.setTime(getDayEnd());
       //当天月份天数加1
       cal.add(Calendar.DAY_OF_MONTH, 1);
       return cal.getTime();
   }

   /**
    * 获取某个日期的开始时间
    *
    * @param d
    * @return
    */
   public static Timestamp getDayStartTime(Date d) {
       Calendar calendar = Calendar.getInstance();
       if (null != d) {
           calendar.setTime(d);
       }
       calendar.set(calendar.get(Calendar.YEAR),
               calendar.get(Calendar.MONTH),
               calendar.get(Calendar.DAY_OF_MONTH), 0, 0, 0);
       calendar.set(Calendar.MILLISECOND, 0);
       return new Timestamp(calendar.getTimeInMillis());
   }

   /**
    * 获取某个日期的结束时间
    *
    * @param d
    * @return
    */
   public static Timestamp getDayEndTime(Date d) {
       Calendar calendar = Calendar.getInstance();
       if (null != d) {
           calendar.setTime(d);
       }
       calendar.set(calendar.get(Calendar.YEAR),
               calendar.get(Calendar.MONTH),
               calendar.get(Calendar.DAY_OF_MONTH), 23, 59, 59);
       calendar.set(Calendar.MILLISECOND, 999);
       return new Timestamp(calendar.getTimeInMillis());
   }

   /**
    * 获取本周的开始时间
    *
    * @return
    */
   public static Date getBeginDayOfWeek() {
       Date date = new Date();
       if (date == null) {
           return null;
       }
       Calendar cal = Calendar.getInstance();
       cal.setTime(date);
       int dayOfWeek = cal.get(Calendar.DAY_OF_WEEK);
       if (dayOfWeek == 1) {
           dayOfWeek += 7;
       }
       cal.add(Calendar.DATE, 2 - dayOfWeek);
       return getDayStartTime(cal.getTime());
   }

   /**
    * 获取本周的结束时间
    *
    * @return
    */
   public static Date getEndDayOfWeek() {
       Calendar cal = Calendar.getInstance();
       cal.setTime(getBeginDayOfWeek());
       cal.add(Calendar.DAY_OF_WEEK, 6);
       Date weekEndSta = cal.getTime();
       return getDayEndTime(weekEndSta);
   }

   /**
    * 获取上周开始时间
    */
   @SuppressWarnings("unused")
   public static Date getBeginDayOfLastWeek() {
       Date date = new Date();
       if (date == null) {
           return null;
       }
       Calendar cal = Calendar.getInstance();
       cal.setTime(date);
       int dayofweek = cal.get(Calendar.DAY_OF_WEEK);
       if (dayofweek == 1) {
           dayofweek += 7;
       }
       cal.add(Calendar.DATE, 2 - dayofweek - 7);
       return getDayStartTime(cal.getTime());
   }

   /**
    * 获取上周的结束时间
    *
    * @return
    */
   public static Date getEndDayOfLastWeek() {
       Calendar cal = Calendar.getInstance();
       cal.setTime(getBeginDayOfLastWeek());
       cal.add(Calendar.DAY_OF_WEEK, 6);
       Date weekEndSta = cal.getTime();
       return getDayEndTime(weekEndSta);
   }

   /**
    * 获取今年是哪一年
    *
    * @return
    */
   public static Integer getNowYear() {
       Date date = new Date();
       GregorianCalendar gc = (GregorianCalendar) Calendar.getInstance();
       gc.setTime(date);
       return Integer.valueOf(gc.get(1));
   }

   /**
    * 获取本月是哪一月
    *
    * @return
    */
   public static int getNowMonth() {
       Date date = new Date();
       GregorianCalendar gc = (GregorianCalendar) Calendar.getInstance();
       gc.setTime(date);
       return gc.get(2) + 1;
   }

   /**
    * 获取本月的开始时间
    *
    * @return
    */
   public static Date getBeginDayOfMonth() {
       Calendar calendar = Calendar.getInstance();
       calendar.set(getNowYear(), getNowMonth() - 1, 1);
       return getDayStartTime(calendar.getTime());
   }

   /**
    * 获取本月的结束时间
    *
    * @return
    */
   public static Date getEndDayOfMonth() {
       Calendar calendar = Calendar.getInstance();
       calendar.set(getNowYear(), getNowMonth() - 1, 1);
       int day = calendar.getActualMaximum(5);
       calendar.set(getNowYear(), getNowMonth() - 1, day);
       return getDayEndTime(calendar.getTime());
   }

   /**
    * 获取上月的开始时间
    *
    * @return
    */
   public static Date getBeginDayOfLastMonth() {
       Calendar calendar = Calendar.getInstance();
       calendar.set(getNowYear(), getNowMonth() - 2, 1);
       return getDayStartTime(calendar.getTime());
   }

   /**
    * 获取上月的结束时间
    *
    * @return
    */
   public static Date getEndDayOfLastMonth() {
       Calendar calendar = Calendar.getInstance();
       calendar.set(getNowYear(), getNowMonth() - 2, 1);
       int day = calendar.getActualMaximum(5);
       calendar.set(getNowYear(), getNowMonth() - 2, day);
       return getDayEndTime(calendar.getTime());
   }

   /**
    * 获取本年的开始时间
    *
    * @return
    */
   public static Date getBeginDayOfYear() {
       Calendar cal = Calendar.getInstance();
       cal.set(Calendar.YEAR, getNowYear());
       cal.set(Calendar.MONTH, Calendar.JANUARY);
       cal.set(Calendar.DATE, 1);
       return getDayStartTime(cal.getTime());
   }

   /**
    * 获取本年的结束时间
    *
    * @return
    */
   public static Date getEndDayOfYear() {
       Calendar cal = Calendar.getInstance();
       cal.set(Calendar.YEAR, getNowYear());
       cal.set(Calendar.MONTH, Calendar.DECEMBER);
       cal.set(Calendar.DATE, 31);
       return getDayEndTime(cal.getTime());
   }

   /**
    * 两个日期相减得到的天数
    *
    * @param beginDate
    * @param endDate
    * @return
    */
   public static int getDiffDays(Date beginDate, Date endDate) {
       if (beginDate == null || endDate == null) {
           throw new IllegalArgumentException("getDiffDays param is null!");
       }
       long diff = (endDate.getTime() - beginDate.getTime()) / (1000 * 60 * 60 * 24);
       int days = new Long(diff).intValue();
       return days;
   }

   /**
    * 两个日期相减得到的毫秒数
    *
    * @param beginDate
    * @param endDate
    * @return
    */
   public static long dateDiff(Date beginDate, Date endDate) {
       long date1ms = beginDate.getTime();
       long date2ms = endDate.getTime();
       return date2ms - date1ms;
   }

   /**
    * 获取两个日期中的最大日起
    *
    * @param beginDate
    * @param endDate
    * @return
    */
   public static Date max(Date beginDate, Date endDate) {
       if (beginDate == null) {
           return endDate;
       }
       if (endDate == null) {
           return beginDate;
       }
       if (beginDate.after(endDate)) {
           //beginDate日期大于endDate
           return beginDate;
       }
       return endDate;
   }

   /**
    * 获取两个日期中的最小日期
    *
    * @param beginDate
    * @param endDate
    * @return
    */
   public static Date min(Date beginDate, Date endDate) {
       if (beginDate == null) {
           return endDate;
       }
       if (endDate == null) {
           return beginDate;
       }
       if (beginDate.after(endDate)) {
           return endDate;
       }
       return beginDate;
   }

   /**
    * 获取某月该季度的第一个月
    *
    * @param date
    * @return
    */
   public static Date getFirstSeasonDate(Date date) {
       final int[] season = {1, 1, 1, 2, 2, 2, 3, 3, 3, 4, 4, 4};
       Calendar cal = Calendar.getInstance();
       cal.setTime(date);
       int sean = season[cal.get(Calendar.MONTH)];
       cal.set(Calendar.MONTH, sean * 3 - 3);
       return cal.getTime();
   }

   /**
    * 返回某个日期下几天的日期
    *
    * @param date
    * @param i
    * @return
    */
   public static Date getNextDay(Date date, int i) {
       Calendar cal = new GregorianCalendar();
       cal.setTime(date);
       cal.set(Calendar.DATE, cal.get(Calendar.DATE) + i);
       return cal.getTime();
   }

   /**
    * 返回某个日期前几天的日期
    *
    * @param date
    * @param i
    * @return
    */
   public static Date getFrontDay(Date date, int i) {
       Calendar cal = new GregorianCalendar();
       cal.setTime(date);
       cal.set(Calendar.DATE, cal.get(Calendar.DATE) - i);
       return cal.getTime();
   }

   /**
    * 获取某年某月按天切片日期集合（某个月间隔多少天的日期集合）
    *
    * @param beginYear
    * @param beginMonth
    * @param k
    * @return
    */
   public static List getTimeList(int beginYear, int beginMonth, int k) {
       List list = new ArrayList();
       Calendar begincal = new GregorianCalendar(beginYear, beginMonth, 1);
       int max = begincal.getActualMaximum(Calendar.DATE);
       for (int i = 1; i < max; i = i + k) {
           list.add(begincal.getTime());
           begincal.add(Calendar.DATE, k);
       }
       begincal = new GregorianCalendar(beginYear, beginMonth, max);
       list.add(begincal.getTime());
       return list;
   }

   /**
    * 获取某年某月到某年某月按天的切片日期集合（间隔天数的集合）
    *
    * @param beginYear
    * @param beginMonth
    * @param endYear
    * @param endMonth
    * @param k
    * @return
    */
   public static List getTimeList(int beginYear, int beginMonth, int endYear, int endMonth, int k) {
       List list = new ArrayList();
       if (beginYear == endYear) {
           for (int j = beginMonth; j <= endMonth; j++) {
               list.add(getTimeList(beginYear, j, k));
           }
       } else {
           {
               for (int j = beginMonth; j < 12; j++) {
                   list.add(getTimeList(beginYear, j, k));
               }
               for (int i = beginYear + 1; i < endYear; i++) {
                   for (int j = 0; j < 12; j++) {
                       list.add(getTimeList(i, j, k));
                   }
               }
               for (int j = 0; j <= endMonth; j++) {
                   list.add(getTimeList(endYear, j, k));
               }
           }
       }
       return list;
   }

   /**
    * 判断当前时间是否在[startTime, endTime]区间，注意时间格式要一致
    *
    * @param nowTime   当前时间
    * @param startTime 开始时间
    * @param endTime   结束时间
    * @return
    */
   public static boolean isEffectiveDate(Date nowTime, Date startTime, Date endTime) {
       if (nowTime.getTime() == startTime.getTime()
               || nowTime.getTime() == endTime.getTime()) {
           return true;
       }
       Calendar date = Calendar.getInstance();
       date.setTime(nowTime);
       Calendar begin = Calendar.getInstance();
       begin.setTime(startTime);
       Calendar end = Calendar.getInstance();
       end.setTime(endTime);

       if (date.after(begin) && date.before(end)) {
           return true;
       } else {
           return false;
       }
   }

   //=================================时间格式转换==========================

   /**
    * date类型进行格式化输出
    *
    * @param pattern
    * @return
    */
   public static String dateNow(String pattern) {
       Date date = new Date();
       if (pattern == null && "".equals(pattern)) {
           return null;
       }
       SimpleDateFormat formatter = new SimpleDateFormat(pattern);
       String dateString = formatter.format(date);
       return dateString;
   }

   /**
    * date类型进行格式化输出
    *
    * @param date
    * @param pattern
    * @return
    */
   public static String dateToString(Date date, String pattern) {
       if (StringUtils.isEmpty(pattern) && date == null) {
           return null;
       }
       SimpleDateFormat formatter = new SimpleDateFormat(pattern);
       String dateString = formatter.format(date);
       return dateString;
   }

   /**
    * date类型进行格式化输出（返回类型：String）
    *
    * @param date
    * @return
    */
   public static String dateFormat(Date date) {
       SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
       String dateString = formatter.format(date);
       return dateString;
   }

   /**
    * date类型进行格式化输出（返回类型：String）
    *
    * @param date
    * @return
    */
   public static String daFormat(Date date) {
       SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
       String dateString = formatter.format(date);
       return dateString;
   }

   /**
    * 时间戳类型进行格式化输出（返回类型：String）
    *
    * @param date
    * @return
    */
   public static String daFormat(Long date) {
       SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
       Date time = new Date(date);
       String dateString = formatter.format(time);
       return dateString;
   }

   /**
    * 时间戳类型进行格式化输出（返回类型：String）
    *
    * @param date
    * @return
    */
   public static String daFormatHHMMSSAddEight(Long date) {
       SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
       Date time =dateAddHours(new Date(date),8);
       String dateString = formatter.format(time);
       return dateString;
   }

   /**
    * 时间戳类型进行格式化输出（返回类型：String）
    *
    * @param date
    * @return
    */
   public static String daFormatHHMMSS(Long date) {
       SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
       Date time = new Date(date);
       String dateString = formatter.format(time);
       return dateString;
   }


   /**
    * 将"2015-08-31 21:08:06"型字符串转化为Date
    *
    * @param str
    * @return
    * @throws ParseException
    */
   public static Date stringToDate(String str) {
       Date date = null;
       try {
           SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
           date = formatter.parse(str);
       } catch (ParseException e) {
           System.out.println(e.getMessage());
           return date;
       }
       return date;
   }
   
   /**
    * 将"2015-08-31 21:08"型字符串转化为Date
    *
    * @param str
    * @return
    * @throws ParseException
    */
   public static Date stringHHmmToDate(String str) {
       Date date = null;
       try {
           SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm");
           date = formatter.parse(str);
       } catch (ParseException e) {
           System.out.println(e.getMessage());
           return date;
       }
       return date;
   }

   /**
    * 将"2015-08-31"型字符串转化为Date
    *
    * @param str
    * @return
    * @throws ParseException
    */
   public static Date stringToDates(String str) {
       Date date = null;
       try {
           SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
           date = formatter.parse(str);
       } catch (ParseException e) {
           return date;
       }
       return date;
   }

   /**
    * 将CST时间类型字符串进行格式化输出
    *
    * @param str
    * @return
    * @throws ParseException
    */
   public static String cstFormat(String str) {
       try {
           SimpleDateFormat formatter = new SimpleDateFormat("EEE MMM dd HH:mm:ss zzz yyyy", Locale.US);
           Date date = formatter.parse(str);
           return dateFormat(date);
       } catch (Exception e) {
           return "";
       }
   }

   /**
    * 将long类型转化为Date
    *
    * @param str
    * @return
    * @throws ParseException
    */
   public static Date longToDate(long str) throws ParseException {
       return new Date(str * 1000);
   }

   //====================================其他常见日期操作方法======================

   /**
    * 判断当前日期是否在[startDate, endDate]区间
    *
    * @param startDate 开始日期
    * @param endDate   结束日期
    * @return
    * <AUTHOR>
    */
   public static boolean isEffectiveDate(Date startDate, Date endDate) {
       if (startDate == null || endDate == null) {
           return false;
       }
       long currentTime = System.currentTimeMillis();
       if (currentTime >= startDate.getTime()
               && currentTime <= endDate.getTime()) {
           return true;
       }
       return false;
   }

   /**
    * 得到二个日期间的间隔天数
    *
    * @param secondString：后一个日期
    * @param firstString：前一个日期
    * @return
    */
   public static String getTwoDay(String secondString, String firstString) {
       SimpleDateFormat myFormatter = new SimpleDateFormat("yyyy-MM-dd");
       long day = 0;
       try {
           Date secondTime = myFormatter.parse(secondString);
           Date firstTime = myFormatter.parse(firstString);
           day = (secondTime.getTime() - firstTime.getTime()) / (24 * 60 * 60 * 1000);
       } catch (Exception e) {
           return "";
       }
       return day + "";
   }

   /**
    * 时间前推或后推分钟,其中JJ表示分钟.
    *
    * @param stringTime：时间
    * @param minute：分钟（有正负之分）
    * @return
    */
   public static String getPreTime(String stringTime, String minute) {
       SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
       String mydate1 = "";
       try {
           Date date1 = format.parse(stringTime);
           long time = (date1.getTime() / 1000) + Integer.parseInt(minute) * 60;
           date1.setTime(time * 1000);
           mydate1 = format.format(date1);
       } catch (Exception e) {
           return "";
       }
       return mydate1;
   }

   /**
    * 时间转成成秒
    */
   public static long getTime(Date data) {
       if (data != null) {
           return (data.getTime() / 1000);
       }
       return 0;
   }

   /**
    * 将短时间格式字符串转换为时间 yyyy-MM-dd
    *
    * @param strDate
    * @return
    */
   public static Date strToDate(String strDate) {
       SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
       ParsePosition pos = new ParsePosition(0);
       Date strtodate = formatter.parse(strDate, pos);
       return strtodate;
   }

   /**
    * 得到一个时间延后或前移几天的时间
    *
    * @param nowdate：时间
    * @param delay：前移或后延的天数
    * @return
    */
   public static String getNextDay(String nowdate, String delay) {
       try {
           SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
           String mdate = "";
           Date d = strToDate(nowdate);
           long myTime = (d.getTime() / 1000) + Integer.parseInt(delay) * 24 * 60 * 60;
           d.setTime(myTime * 1000);
           mdate = format.format(d);
           return mdate;
       } catch (Exception e) {
           return "";
       }
   }

   /**
    * 判断是否闰年
    *
    * @param ddate
    * @return
    */
   public static boolean isLeapYear(String ddate) {
       /**
        * 详细设计： 1.被400整除是闰年，否则： 2.不能被4整除则不是闰年 3.能被4整除同时不能被100整除则是闰年
        * 3.能被4整除同时能被100整除则不是闰年
        */
       Date d = strToDate(ddate);
       GregorianCalendar gc = (GregorianCalendar) Calendar.getInstance();
       gc.setTime(d);
       int year = gc.get(Calendar.YEAR);
       if ((year % 400) == 0) {
           return true;
       } else if ((year % 4) == 0) {
           if ((year % 100) == 0) {
               return false;
           } else {
               return true;
           }
       } else {
           return false;
       }
   }

   /**
    * 返回美国时间格式
    *
    * @param str
    * @return
    */
   public static String geteDate(String str) {
       SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
       ParsePosition pos = new ParsePosition(0);
       Date strtodate = formatter.parse(str, pos);
       String j = strtodate.toString();
       String[] k = j.split(" ");
       return k[2] + k[1].toUpperCase() + k[5].substring(2, 4);
   }

   /**
    * 判断二个时间是否在同一个周
    *
    * @param date1
    * @param date2
    * @return
    */
   public static boolean isSameWeekDates(Date date1, Date date2) {
       Calendar cal1 = Calendar.getInstance();
       Calendar cal2 = Calendar.getInstance();
       cal1.setTime(date1);
       cal2.setTime(date2);
       int subYear = cal1.get(Calendar.YEAR) - cal2.get(Calendar.YEAR);
       if (0 == subYear) {
           if (cal1.get(Calendar.WEEK_OF_YEAR) == cal2.get(Calendar.WEEK_OF_YEAR)) {
               return true;
           }
       } else if (1 == subYear && 11 == cal2.get(Calendar.MONTH)) {
           // 如果12月的最后一周横跨来年第一周的话则最后一周即算做来年的第一周
           if (cal1.get(Calendar.WEEK_OF_YEAR) == cal2.get(Calendar.WEEK_OF_YEAR)) {
               return true;
           }
       } else if (-1 == subYear && 11 == cal1.get(Calendar.MONTH)) {
           if (cal1.get(Calendar.WEEK_OF_YEAR) == cal2.get(Calendar.WEEK_OF_YEAR)) {
               return true;
           }
       }
       return false;
   }

   /**
    * 产生周序列,即得到当前时间所在的年度是第几周
    *
    * @return
    */
   public static String getSeqWeek() {
       Calendar c = Calendar.getInstance(Locale.CHINA);
       String week = Integer.toString(c.get(Calendar.WEEK_OF_YEAR));
       if (week.length() == 1) {
           week = "0" + week;
       }
       String year = Integer.toString(c.get(Calendar.YEAR));
       return year + "年第" + week + "周";
   }

   /**
    * 获得一个日期所在的周的星期几的日期，如要找出2002年2月3日所在周的星期一是几号
    *
    * @param sdate：日期
    * @param num：星期几（星期天是一周的第一天）
    * @return
    */
   public static String getWeek(String sdate, String num) {
       // 再转换为时间
       Date dd = strToDate(sdate);
       Calendar c = Calendar.getInstance();
       c.setTime(dd);
       if ("1".equals(num))
       {
           // 返回星期一所在的日期
           c.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
       } else if ("2".equals(num))
       {
           // 返回星期二所在的日期
           c.set(Calendar.DAY_OF_WEEK, Calendar.TUESDAY);
       } else if ("3".equals(num))
       {
           // 返回星期三所在的日期
           c.set(Calendar.DAY_OF_WEEK, Calendar.WEDNESDAY);
       } else if ("4".equals(num))
       {
           // 返回星期四所在的日期
           c.set(Calendar.DAY_OF_WEEK, Calendar.THURSDAY);
       } else if ("5".equals(num))
       {
           // 返回星期五所在的日期
           c.set(Calendar.DAY_OF_WEEK, Calendar.FRIDAY);
       } else if ("6".equals(num))
       {
           // 返回星期六所在的日期
           c.set(Calendar.DAY_OF_WEEK, Calendar.SATURDAY);
       } else if ("0".equals(num))
       {
           // 返回星期日所在的日期
           c.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY);
       }
       return new SimpleDateFormat("yyyy-MM-dd").format(c.getTime());
   }

   /**
    * 根据一个日期，返回是星期几的字符串
    *
    * @param sdate
    * @return
    */
   public static String getWeek(String sdate) {
       // 再转换为时间
       Date date = strToDate(sdate);
       Calendar c = Calendar.getInstance();
       c.setTime(date);
       // hour中存的就是星期几了，其范围 1~7
       // 1=星期日 7=星期六，其他类推
       return new SimpleDateFormat("EEEE").format(c.getTime());
   }

   /**
    * 根据一个日期，返回是星期几的字符串
    *
    * @param sdate
    * @return
    */
   public static String getWeekStr(String sdate) {
       String str = "";
       str = getWeek(sdate);
       if ("1".equals(str)) {
           str = "星期日";
       } else if ("2".equals(str)) {
           str = "星期一";
       } else if ("3".equals(str)) {
           str = "星期二";
       } else if ("4".equals(str)) {
           str = "星期三";
       } else if ("5".equals(str)) {
           str = "星期四";
       } else if ("6".equals(str)) {
           str = "星期五";
       } else if ("7".equals(str)) {
           str = "星期六";
       }
       return str;
   }

   /**
    * 两个时间之间的天数
    *
    * @param date1
    * @param date2
    * @return
    */
   public static long getDays(String date1, String date2) {
       if (date1 == null || "".equals(date1)) {
           return 0;
       }
       if (date2 == null || "".equals(date2)) {
           return 0;
       }
       // 转换为标准时间
       SimpleDateFormat myFormatter = new SimpleDateFormat("yyyy-MM-dd");
       Date date = null;
       Date mydate = null;
       try {
           date = myFormatter.parse(date1);
           mydate = myFormatter.parse(date2);
       } catch (Exception e) {
       }
       long day = (date.getTime() - mydate.getTime()) / (24 * 60 * 60 * 1000);
       return day;
   }

   /**
    * 形成如下的日历 ， 根据传入的一个时间返回一个结构 星期日 星期一 星期二 星期三 星期四 星期五 星期六 下面是当月的各个时间
    * 此函数返回该日历第一行星期日所在的日期
    *
    * @param sdate
    * @return
    */
   public static String getNowMonth(String sdate) {
       // 取该时间所在月的一号
       sdate = sdate.substring(0, 8) + "01";
       // 得到这个月的1号是星期几
       Date date = strToDate(sdate);
       Calendar c = Calendar.getInstance();
       c.setTime(date);
       int u = c.get(Calendar.DAY_OF_WEEK);
       String newday = getNextDay(sdate, (1 - u) + "");
       return newday;
   }

   /**
    * 根据用户传入的时间表示格式，返回当前时间的格式 如果是yyyyMMdd，注意字母y不能大写
    *
    * @param sformat
    * @return
    */
   public static String getUserDate(String sformat) {
       Date currentTime = new Date();
       SimpleDateFormat formatter = new SimpleDateFormat(sformat);
       String dateString = formatter.format(currentTime);
       return dateString;
   }

   /**
    * 根据用户传入的时间表示格式，返回当前时间的格式 如果是yyyyMMdd，注意字母y不能大写
    *
    * @param sformat
    * @return
    */
   public static String getDateString(Date date, String sformat) {
       Date currentTime = null;
       if (date == null) {
           currentTime = new Date();
       } else {
           currentTime = date;
       }
       SimpleDateFormat formatter = new SimpleDateFormat(sformat);
       String dateString = formatter.format(currentTime);
       return dateString;
   }

   /**
    * 返回一个i位数的随机数
    *
    * @param i
    * @return
    */
   public static String getRandom(int i) {
       Random jjj = new Random();
       if (i == 0) {
           return "";
       }
       String jj = "";
       for (int k = 0; k < i; k++) {
           jj = jj + jjj.nextInt(9);
       }
       return jj;
   }

   //====================================日期格式转换======================

   /**
    * Date转换为LocalDateTime
    *
    * @param date
    */
   public static LocalDateTime dateToLocalDateTime(Date date) {
       if (date != null) {
           //An instantaneous point on the time-line.(时间线上的一个瞬时点。)
           Instant instant = date.toInstant();
           //A time-zone ID, such ZxingCodeUtil {@code Europe/Paris}.(时区)
           ZoneId zoneId = ZoneId.systemDefault();
           LocalDateTime localDateTime = instant.atZone(zoneId).toLocalDateTime();
           return localDateTime;
       }
       return null;
   }

   /**
    * LocalDateTime转换为Date
    *
    * @param localDateTime
    */
   public static Date localDateTimeToDate(LocalDateTime localDateTime) {
       if (localDateTime != null) {
           ZoneId zoneId = ZoneId.systemDefault();
           //Combines this date-time with a time-zone to create a  ZonedDateTime.
           ZonedDateTime zdt = localDateTime.atZone(zoneId);
           Date date = Date.from(zdt.toInstant());
           return date;
       }
       return null;
   }

   //====================================当前时间加减======================

   /**
    * 时间加减小时
    *
    * @param startDate 要处理的时间，Null则为当前时间
    * @param hours     加减的小时
    * @return Date
    */
   public static Date dateAddHours(Date startDate, int hours) {
       if (startDate == null) {
           startDate = new Date();
       }
       Calendar c = Calendar.getInstance();
       c.setTime(startDate);
       c.set(Calendar.HOUR, c.get(Calendar.HOUR) + hours);
       return c.getTime();
   }

   /**
    * 时间加减分钟
    *
    * @param startDate 要处理的时间，Null则为当前时间
    * @param minutes   加减的分钟
    * @return Date
    */
   public static Date dateAddMinutes(Date startDate, int minutes) {
       if (startDate == null) {
           startDate = new Date();
       }
       Calendar c = Calendar.getInstance();
       c.setTime(startDate);
       c.set(Calendar.MINUTE, c.get(Calendar.MINUTE) + minutes);
       return c.getTime();
   }

   /**
    * 时间加减秒数
    *
    * @param startDate 要处理的时间，Null则为当前时间
    * @param seconds   加减的秒数
    * @return Date
    */
   public static Date dateAddSeconds(Date startDate, int seconds) {
       if (startDate == null) {
           startDate = new Date();
       }
       Calendar c = Calendar.getInstance();
       c.setTime(startDate);
       c.set(Calendar.SECOND, c.get(Calendar.SECOND) + seconds);
       return c.getTime();
   }

   /**
    * 时间加减年数
    *
    * @param startDate 要处理的时间，Null则为当前时间
    * @param years     加减的年数
    * @return Date
    */
   public static Date dateAddYears(Date startDate, int years) {
       if (startDate == null) {
           startDate = new Date();
       }
       Calendar c = Calendar.getInstance();
       c.setTime(startDate);
       c.set(Calendar.YEAR, c.get(Calendar.YEAR) + years);
       return c.getTime();
   }

   /**
    * 时间加减月数
    *
    * @param startDate 要处理的时间，Null则为当前时间
    * @param months    加减的月数
    * @return Date
    */
   public static Date dateAddMonths(Date startDate, int months) {
       if (startDate == null) {
           startDate = new Date();
       }
       Calendar c = Calendar.getInstance();
       c.setTime(startDate);
       c.set(Calendar.MONTH, c.get(Calendar.MONTH) + months);
       return c.getTime();
   }

   /**
    * 时间加减天数
    *
    * @param startDate 要处理的时间，Null则为当前时间
    * @param days      加减的天数
    * @return Date
    */
   public static Date dateAddDays(Date startDate, int days) {
       if (startDate == null) {
           startDate = new Date();
       }
       Calendar c = Calendar.getInstance();
       c.setTime(startDate);
       c.set(Calendar.DATE, c.get(Calendar.DATE) + days);
       return c.getTime();
   }

   /**
    * 时间加减小时
    *
    * @param localDateTime 要处理的时间，Null则为当前时间
    * @param hours         加减的小时
    * @return Date
    */
   public static LocalDateTime localDateAddHours(LocalDateTime localDateTime, int hours) {
       if (localDateTime == null) {
           localDateTime = LocalDateTime.now();
       }
       return localDateTime.plusHours(hours);
   }

   /**
    * 时间加减分钟
    *
    * @param localDateTime 要处理的时间，Null则为当前时间
    * @param minutes       加减的分钟
    * @return Date
    */
   public static LocalDateTime localDateAddMinutes(LocalDateTime localDateTime, int minutes) {
       if (localDateTime == null) {
           localDateTime = LocalDateTime.now();
       }
       return localDateTime.plusMinutes(minutes);
   }

   /**
    * 时间加减秒数
    *
    * @param localDateTime 要处理的时间，Null则为当前时间
    * @param seconds       加减的秒数
    * @return Date
    */
   public static LocalDateTime localDateAddSeconds(LocalDateTime localDateTime, int seconds) {
       if (localDateTime == null) {
           localDateTime = LocalDateTime.now();
       }
       return localDateTime.plusSeconds(seconds);
   }

   /**
    * 时间加减年数
    *
    * @param localDateTime 要处理的时间，Null则为当前时间
    * @param years         加减的年数
    * @return Date
    */
   public static LocalDateTime localDateAddYears(LocalDateTime localDateTime, int years) {
       if (localDateTime == null) {
           localDateTime = LocalDateTime.now();
       }
       return localDateTime.plusYears(years);
   }

   /**
    * 时间加减月数
    *
    * @param localDateTime 要处理的时间，Null则为当前时间
    * @param months        加减的月数
    * @return Date
    */
   public static LocalDateTime localDateMonths(LocalDateTime localDateTime, int months) {
       if (localDateTime == null) {
           localDateTime = LocalDateTime.now();
       }
       return localDateTime.plusMonths(months);
   }

   /**
    * 时间加减天数
    *
    * @param localDateTime 要处理的时间，Null则为当前时间
    * @param days          加减的天数
    * @return Date
    */
   public static LocalDateTime localDateAddDays(LocalDateTime localDateTime, int days) {
       if (localDateTime == null) {
           localDateTime = LocalDateTime.now();
       }
       return localDateTime.plusDays(days);
   }

   //====================================时间比较======================

   /**
    * 时间比较（如果myDate>compareDate返回1，<返回-1，相等返回0）
    *
    * @param myDate      时间
    * @param compareDate 要比较的时间
    * @return int
    */
   public static int dateCompare(Date myDate, Date compareDate) {
       Calendar myCal = Calendar.getInstance();
       Calendar compareCal = Calendar.getInstance();
       myCal.setTime(myDate);
       compareCal.setTime(compareDate);
       return myCal.compareTo(compareCal);
   }

   /**
    * 获取两个时间中最小的一个时间
    *
    * @param date
    * @param compareDate
    * @return Date
    */
   public static Date dateMin(Date date, Date compareDate) {
       if (date == null) {
           return compareDate;
       }
       if (compareDate == null) {
           return date;
       }
       if (1 == dateCompare(date, compareDate)) {
           return compareDate;
       } else if (-1 == dateCompare(date, compareDate)) {
           return date;
       }
       return date;
   }

   /**
    * 获取两个时间中最大的一个时间
    *
    * @param date
    * @param compareDate
    * @return Date
    */
   public static Date dateMax(Date date, Date compareDate) {
       if (date == null) {
           return compareDate;
       }
       if (compareDate == null) {
           return date;
       }
       if (1 == dateCompare(date, compareDate)) {
           return date;
       } else if (-1 == dateCompare(date, compareDate)) {
           return compareDate;
       }
       return date;
   }

   /**
    * 获取时间当年某个月的最后一天
    *
    * @param startDate
    * @param month     月份
    * @return int 天数
    */
   public static int getLastDayOfMonth(Date startDate, int month) {
       if (startDate == null) {
           startDate = new Date();
       }
       Calendar c = Calendar.getInstance();
       c.setTime(startDate);
       c.set(c.get(Calendar.YEAR), month, 1);
       c.add(Calendar.DATE, -1);
       int day = c.get(Calendar.DAY_OF_MONTH);
       return day;
   }

   /**
    * 获取固定日期范围内的所有日期，以数组形式返回
    */
   public static List<Date> getAllDays(Date startTime, Date endTime) {
       List<Date> listDay = new ArrayList<>();
       Date dtDay = new Date();
       for (dtDay = startTime; dtDay.compareTo(endTime) <= 0; dtDay = DateUtil.dateAddDays(dtDay, 1)) {
           listDay.add(dtDay);
       }
       return listDay;
   }

   //====================================通过cron转换成时间======================

   /**
    * 获取最近几次的运行时间
    *
    * @param cron     表达式
    * @param numTimes 次数
    * @return
    */
   public static List<String> getRecentExecTime(String cron, int numTimes) {
       List<String> list = new ArrayList<>();
       try {
           CronTriggerImpl cronTriggerImpl = new CronTriggerImpl();
           cronTriggerImpl.setCronExpression(cron);
           // 这个是重点，一行代码搞定
           List<Date> dates = TriggerUtils.computeFireTimes(cronTriggerImpl, null, numTimes);
           SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
           for (Date date : dates) {
               list.add(dateFormat.format(date));
           }
       } catch (ParseException e) {
           e.getMessage();
       }
       return list;
   }

   /**
    * 获取从某个时间段下一次执行的运行时间
    *
    * @param cron  表达式
    * @param dates 日期
    * @return
    */
   public static String getNextCron(String cron, Date dates) {
       String crons = null;
       if (dates == null) {
           dates = new Date();
       }
       if (StringUtils.isEmpty(cron)) {
           return crons;
       }
       CronExpression cronExpression = null;
       try {
           cronExpression = new CronExpression(cron);
       } catch (ParseException e) {
           System.out.println(e.getMessage());
           return crons;
       }
       Date date = cronExpression.getNextValidTimeAfter(dates);
       if (date != null) {
           crons = dateFormat(date);
       }
       return crons;
   }

   /**
    * 获取从某个时间段下一次执行的运行时间
    *
    * @param cron  表达式
    * @param dates 日期
    * @return
    */
   public static Date getNextCronDate(String cron, Date dates) {
       Date crons = new Date();
       if (dates == null) {
           dates = new Date();
       }
       if (StringUtils.isEmpty(cron)) {
           return crons;
       }
       CronExpression cronExpression = null;
       try {
           cronExpression = new CronExpression(cron);
       } catch (ParseException e) {
           System.out.println(e.getMessage());
           return crons;
       }
       Date date = cronExpression.getNextValidTimeAfter(dates);
       return date;
   }

   /**
    * 时间转成cron
    *
    * @return
    */
   public static String getDateToCron(Date date) {
       String cron = null;
       String dateFormat = "ss mm HH dd MM ?";
       if (date == null) {
           cron = dateNow(dateFormat);
       } else {
           cron = dateToString(date, dateFormat);
       }
       return cron;
   }

   public static boolean timeCalendar(Date nowTime, Date dayTimeStart, Date dayTimeEnd) {
       //设置当前时间
       Calendar date = Calendar.getInstance();
       date.setTime(nowTime);
       //设置开始时间
       Calendar timeStart = Calendar.getInstance();
       timeStart.setTime(dayTimeStart);
       //设置结束时间
       Calendar timeEnd = Calendar.getInstance();
       timeEnd.setTime(dayTimeEnd);
       if (date.equals(timeStart) || date.equals(timeEnd)) {
           return true;
       }
       if ((date.after(timeStart) && date.before(timeEnd))) {
           return true;
       } else {
           return false;
       }
   }

   public static boolean isValidDate(String str) {
       boolean convertSuccess=true;
       // 指定日期格式为四位年/两位月份/两位日期，注意yyyy/MM/dd区分大小写；
       SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
       if(str.length()==10){
           format = new SimpleDateFormat("yyyy-MM-dd");
       }
       try {
           //设置lenient为false. 否则SimpleDateFormat会比较宽松地验证日期，比如2007/02/29会被接受，并转换成2007/03/01
           format.parse(str);
       } catch (ParseException e) {
           // 如果throw java.text.ParseException或者NullPointerException，就说明格式不对
           convertSuccess=false;
       }
       return convertSuccess;
   }


   public static boolean isOverlap(Date leftStartDate, Date leftEndDate, Date rightStartDate, Date rightEndDate) {

       return ((leftStartDate.getTime() >= rightStartDate.getTime())
               && leftStartDate.getTime() < rightEndDate.getTime())
               ||
               ((leftStartDate.getTime() > rightStartDate.getTime())
                       && leftStartDate.getTime() <= rightEndDate.getTime())
               ||
               ((rightStartDate.getTime() >= leftStartDate.getTime())
                       && rightStartDate.getTime() < leftEndDate.getTime())
               ||
               ((rightStartDate.getTime() > leftStartDate.getTime())
                       && rightStartDate.getTime() <= leftEndDate.getTime());

   }

   /**
    * 时间范围添加连接符
    * @param jsonArray
    * @param type
    * @param format
    * @return
    */
   public static JSONArray addCon(JSONArray jsonArray,String type, String format) {

       if("timeRange".equals(type)){
           String value1 = jsonArray.get(0).toString();
           String value2 = jsonArray.get(1).toString();
           jsonArray.clear();
           jsonArray.add(value1 + "至");
           jsonArray.add(value2);
       }
       if("dateRange".equals(type)){
           DateTimeFormatter ftfDateRange =DateTimeFormatter.ofPattern(format);
           long date1 = Long.parseLong(String.valueOf(jsonArray.get(0)));
           long date2 = Long.parseLong(String.valueOf(jsonArray.get(1)));
           String value1 = ftfDateRange.format(LocalDateTime.ofInstant(Instant.ofEpochMilli(date1), ZoneId.systemDefault()));
           String value2 = ftfDateRange.format(LocalDateTime.ofInstant(Instant.ofEpochMilli(date2), ZoneId.systemDefault()));
           jsonArray.clear();
           jsonArray.add(value1 + "至");
           jsonArray.add(value2);
       }
       return jsonArray;
   }
   /** 获取时间戳 */
   public static Long getMillis2() {
       return Instant.now().toEpochMilli();
   }

   /** LocalDateTime转时间戳 */
   public static Long localDateTime2Millis(LocalDateTime localDateTime) {
       return localDateTime.toInstant(ZoneOffset.ofHours(8)).toEpochMilli();
   }

   /** LocalDate转时间戳 */
   public static Long localDate2Millis(LocalDate localDate) {
       return LocalDateTime.of(localDate, LocalTime.MIN).toInstant(ZoneOffset.ofHours(8)).toEpochMilli();
   }

   /** Clock转时间戳 */
   public static Long clock2Millis(Clock clock) {
       return clock.millis();
   }

   /** ZoneDateTIme转时间戳(这个不常用吧~) */
   public static Long zoneDateTime2Millis(ZonedDateTime zonedDateTime) {
       return zonedDateTime.toLocalDateTime().toInstant(ZoneOffset.ofHours(8)).toEpochMilli();
   }

   /** String转时间戳(JDK8) */
   public static Long string2MillisWithJdk8(String dateStr , String formatStr) {
       return LocalDateTime.parse(dateStr , DateTimeFormatter.ofPattern(formatStr)).toInstant(ZoneOffset.ofHours(8)).toEpochMilli();
   }
   
   /**
	 * 对输入日期进行增减
	 *
	 * @param date      输入日期
	 * @param field     1则代表的是对年份操作，2是对月份操作，3是对星期操作，5是对日期操作，11是对小时操作，12是对分钟操作，13是对秒操作，14是对毫秒操作
	 * @param increment 要增减的值
	 *           e.g. 增加一天 （date,11,24） or (date,5,1)
	 */
	public static Date add(Date date, int field, int increment) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);

		cal.add(field, increment);
		return cal.getTime();
	}
	
	public static List<String> findDaysStr(String begintTime, String endTime) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		Date dBegin = null;
		Date dEnd = null;
		try {
			dBegin = sdf.parse(begintTime);
			dEnd = sdf.parse(endTime);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		List<String> daysStrList = new ArrayList<String>();
		daysStrList.add(sdf.format(dBegin));
		Calendar calBegin = Calendar.getInstance();
		calBegin.setTime(dBegin);
		Calendar calEnd = Calendar.getInstance();
		calEnd.setTime(dEnd);
		while (dEnd.after(calBegin.getTime())) {
			calBegin.add(Calendar.DAY_OF_MONTH, 1);
			String dayStr = sdf.format(calBegin.getTime());
			daysStrList.add(dayStr);
		}
		return daysStrList;
	}

}
