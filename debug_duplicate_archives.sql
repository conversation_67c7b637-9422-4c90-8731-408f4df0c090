-- 调试和查找重复档案名称的SQL查询
-- 目标：查找相同根节点下，有没有名字相同的子节点，且必须在一个分类下面

-- 第一步：检查数据是否存在
-- 先检查目标档案名称是否存在
SELECT '=== 检查目标档案名称是否存在 ===' as debug_info;
SELECT 
    archives_id, 
    parent_id, 
    archives_name, 
    archives_type,
    is_sz,
    LENGTH(parent_id) as parent_len,
    CASE 
        WHEN parent_id IS NULL OR parent_id = '' THEN '根节点'
        WHEN LENGTH(TRIM(parent_id)) <= 3 THEN '可能是根节点'
        ELSE '子节点'
    END as node_type
FROM dgdoc_archives 
WHERE is_sz = 0 
  AND archives_type = '1' 
  AND (
    archives_name LIKE '%基坑支护%' OR 
    archives_name LIKE '%桩基%' OR
    archives_name LIKE '%地基与基础%' OR
    archives_name LIKE '%建筑装饰装修%' OR
    archives_name LIKE '%屋面%' OR
    archives_name LIKE '%通风与空调%' OR
    archives_name LIKE '%建筑电气%' OR
    archives_name LIKE '%智能建筑%'
  )
ORDER BY archives_name
LIMIT 20;

-- 第二步：检查parent_id的分布情况
SELECT '=== 检查parent_id的分布情况 ===' as debug_info;
SELECT 
    CASE 
        WHEN parent_id IS NULL THEN 'NULL'
        WHEN parent_id = '' THEN 'EMPTY'
        WHEN LENGTH(TRIM(parent_id)) = 1 THEN 'LENGTH_1'
        WHEN LENGTH(TRIM(parent_id)) <= 3 THEN 'LENGTH_2_3'
        ELSE 'LENGTH_GT_3'
    END as parent_type,
    COUNT(*) as count
FROM dgdoc_archives 
WHERE is_sz = 0 AND archives_type = '1'
GROUP BY 
    CASE 
        WHEN parent_id IS NULL THEN 'NULL'
        WHEN parent_id = '' THEN 'EMPTY'
        WHEN LENGTH(TRIM(parent_id)) = 1 THEN 'LENGTH_1'
        WHEN LENGTH(TRIM(parent_id)) <= 3 THEN 'LENGTH_2_3'
        ELSE 'LENGTH_GT_3'
    END
ORDER BY count DESC;

-- 第三步：优化后的重复档案查询
SELECT '=== 查找重复档案名称 ===' as debug_info;

WITH RECURSIVE
-- 档案树结构（从根节点开始递归）
archive_tree AS (
    -- 基础查询：根节点
    SELECT
        da1.archives_id,
        da1.parent_id,
        da1.archives_name,
        da1.archives_type,
        da1.is_sz,
        1 AS level,
        CAST(da1.archives_name AS CHAR(1000)) AS path,
        CAST(da1.archives_id AS CHAR(1000)) AS id_path,
        da1.archives_name AS root_name,
        da1.archives_id AS root_id
    FROM
        dgdoc_archives da1
    WHERE
        -- 根节点条件（调整为更宽松的条件）
        (da1.parent_id IS NULL OR da1.parent_id = '' OR LENGTH(TRIM(da1.parent_id)) <= 3)
      AND da1.is_sz = 0
      AND da1.archives_type = '1'
      AND da1.archives_name IN (
                                '基坑支护子分部工程验收资料抽查表',
                                '桩基分项工程验收资料抽查表',
                                '地基与基础分部工程验收资料抽查表',
                                '建筑装饰装修分部工程验收资料抽查表',
                                '屋面分部工程验收资料抽查表',
                                '通风与空调分部工程验收资料抽查表',
                                '建筑电气分部工程验收资料抽查表',
                                '智能建筑分部工程验收资料抽查表'
        )

    UNION ALL

    -- 递归查询：子节点
    SELECT
        da2.archives_id,
        da2.parent_id,
        da2.archives_name,
        da2.archives_type,
        da2.is_sz,
        nt.level + 1,
        CONCAT(nt.path, ' -> ', da2.archives_name),
        CONCAT(nt.id_path, ',', da2.archives_id),
        nt.root_name,
        nt.root_id
    FROM
        dgdoc_archives da2
    INNER JOIN archive_tree nt ON da2.parent_id = nt.archives_id
    WHERE
        da2.is_sz = 0
      AND da2.archives_type = '1'
      AND nt.level < 10  -- 防止无限递归
),

-- 查找重复的档案名称
duplicate_names AS (
    SELECT 
        root_id,
        root_name,
        archives_name,
        COUNT(*) as duplicate_count
    FROM archive_tree
    WHERE level > 1  -- 只检查子节点
    GROUP BY root_id, root_name, archives_name
    HAVING COUNT(*) > 1
)

-- 最终结果：显示重复的档案
SELECT 
    dn.root_name as '根节点名称',
    dn.archives_name as '重复的档案名称',
    dn.duplicate_count as '重复次数',
    GROUP_CONCAT(
        CONCAT('ID:', at.archives_id, ' 路径:', at.path) 
        SEPARATOR ' | '
    ) as '重复档案详情'
FROM duplicate_names dn
JOIN archive_tree at ON dn.root_id = at.root_id 
                    AND dn.archives_name = at.archives_name
                    AND at.level > 1
GROUP BY dn.root_id, dn.root_name, dn.archives_name, dn.duplicate_count
ORDER BY dn.root_name, dn.duplicate_count DESC;
