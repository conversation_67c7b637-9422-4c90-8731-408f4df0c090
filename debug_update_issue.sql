-- UPDATE语句问题排查脚本
-- 逐步检查每个环节，找出匹配不上的原因

-- ========================================
-- 第1步：检查临时表数据
-- ========================================
SELECT '=== 第1步：检查temp_matched_archives表数据 ===' as debug_step;

-- 检查临时表是否有数据
SELECT 
    COUNT(*) as total_records,
    COUNT(DISTINCT new_archives_id) as unique_new_ids,
    COUNT(DISTINCT old_archives_id) as unique_old_ids
FROM temp_matched_archives;

-- 查看临时表前几条数据
SELECT 
    new_archives_id,
    old_archives_id
FROM temp_matched_archives 
LIMIT 5;

-- ========================================
-- 第2步：检查新档案实例表
-- ========================================
SELECT '=== 第2步：检查dgdoc_archives_instance表 ===' as debug_step;

-- 检查指定项目的新档案实例数据
SELECT 
    COUNT(*) as total_new_instances,
    COUNT(DISTINCT archives_id) as unique_archives_ids
FROM dgdoc_archives_instance 
WHERE project_monomer_id = '4d10c10a6b292568ee32642e364a3022';

-- 检查临时表中的new_archives_id是否在新档案实例表中存在
SELECT 
    COUNT(*) as matched_new_archives
FROM temp_matched_archives tma
INNER JOIN dgdoc_archives_instance nai ON tma.new_archives_id = nai.archives_id
WHERE nai.project_monomer_id = '4d10c10a6b292568ee32642e364a3022';

-- ========================================
-- 第3步：检查旧档案实例表
-- ========================================
SELECT '=== 第3步：检查dgdoc_archives_instance_old表 ===' as debug_step;

-- 检查指定项目的旧档案实例数据
SELECT 
    COUNT(*) as total_old_instances,
    COUNT(DISTINCT archives_id) as unique_archives_ids
FROM dgdoc_archives_instance_old 
WHERE project_monomer_id = '4d10c10a6b292568ee32642e364a3022';

-- 检查临时表中的old_archives_id是否在旧档案实例表中存在
SELECT 
    COUNT(*) as matched_old_archives
FROM temp_matched_archives tma
INNER JOIN dgdoc_archives_instance_old oai ON tma.old_archives_id = oai.archives_id
WHERE oai.project_monomer_id = '4d10c10a6b292568ee32642e364a3022';

-- ========================================
-- 第4步：检查三表联合匹配
-- ========================================
SELECT '=== 第4步：检查三表联合匹配情况 ===' as debug_step;

-- 检查能够三表联合匹配的记录数
SELECT 
    COUNT(*) as three_table_matches
FROM temp_matched_archives tma
INNER JOIN dgdoc_archives_instance nai ON tma.new_archives_id = nai.archives_id
INNER JOIN dgdoc_archives_instance_old oai ON tma.old_archives_id = oai.archives_id
WHERE nai.project_monomer_id = '4d10c10a6b292568ee32642e364a3022' 
  AND oai.project_monomer_id = '4d10c10a6b292568ee32642e364a3022';

-- ========================================
-- 第5步：详细分析匹配失败的原因
-- ========================================
SELECT '=== 第5步：分析匹配失败原因 ===' as debug_step;

-- 检查临时表中new_archives_id在新档案表中不存在的记录
SELECT 
    'new_archives_id不存在' as issue_type,
    COUNT(*) as count
FROM temp_matched_archives tma
LEFT JOIN dgdoc_archives_instance nai ON tma.new_archives_id = nai.archives_id
WHERE nai.archives_id IS NULL;

-- 检查临时表中old_archives_id在旧档案表中不存在的记录
SELECT 
    'old_archives_id不存在' as issue_type,
    COUNT(*) as count
FROM temp_matched_archives tma
LEFT JOIN dgdoc_archives_instance_old oai ON tma.old_archives_id = oai.archives_id
WHERE oai.archives_id IS NULL;

-- 检查project_monomer_id不匹配的情况
SELECT 
    'project_monomer_id不匹配' as issue_type,
    COUNT(*) as count
FROM temp_matched_archives tma
INNER JOIN dgdoc_archives_instance nai ON tma.new_archives_id = nai.archives_id
INNER JOIN dgdoc_archives_instance_old oai ON tma.old_archives_id = oai.archives_id
WHERE nai.project_monomer_id != '4d10c10a6b292568ee32642e364a3022' 
   OR oai.project_monomer_id != '4d10c10a6b292568ee32642e364a3022';

-- ========================================
-- 第6步：查看具体的不匹配数据
-- ========================================
SELECT '=== 第6步：查看具体不匹配数据 ===' as debug_step;

-- 查看临时表中无法匹配的具体记录
SELECT 
    tma.new_archives_id,
    tma.old_archives_id,
    CASE WHEN nai.archives_id IS NULL THEN '新档案ID不存在' ELSE '新档案ID存在' END as new_status,
    CASE WHEN oai.archives_id IS NULL THEN '旧档案ID不存在' ELSE '旧档案ID存在' END as old_status,
    nai.project_monomer_id as new_project_id,
    oai.project_monomer_id as old_project_id,
    nai.archives_name as new_name,
    oai.archives_name as old_name
FROM temp_matched_archives tma
LEFT JOIN dgdoc_archives_instance nai ON tma.new_archives_id = nai.archives_id
LEFT JOIN dgdoc_archives_instance_old oai ON tma.old_archives_id = oai.archives_id
WHERE nai.archives_id IS NULL 
   OR oai.archives_id IS NULL 
   OR nai.project_monomer_id != '4d10c10a6b292568ee32642e364a3022'
   OR oai.project_monomer_id != '4d10c10a6b292568ee32642e364a3022'
LIMIT 10;

-- ========================================
-- 第7步：检查archives_type匹配情况
-- ========================================
SELECT '=== 第7步：检查archives_type匹配 ===' as debug_step;

-- 检查archives_type不匹配的情况
SELECT 
    nai.archives_type as new_type,
    oai.archives_type as old_type,
    COUNT(*) as count
FROM temp_matched_archives tma
INNER JOIN dgdoc_archives_instance nai ON tma.new_archives_id = nai.archives_id
INNER JOIN dgdoc_archives_instance_old oai ON tma.old_archives_id = oai.archives_id
WHERE nai.project_monomer_id = '4d10c10a6b292568ee32642e364a3022' 
  AND oai.project_monomer_id = '4d10c10a6b292568ee32642e364a3022'
  AND nai.archives_type != oai.archives_type
GROUP BY nai.archives_type, oai.archives_type;

-- ========================================
-- 第8步：生成修复建议
-- ========================================
SELECT '=== 第8步：修复建议 ===' as debug_step;

-- 统计各种问题的数量
SELECT 
    SUM(CASE WHEN nai.archives_id IS NULL THEN 1 ELSE 0 END) as new_id_missing,
    SUM(CASE WHEN oai.archives_id IS NULL THEN 1 ELSE 0 END) as old_id_missing,
    SUM(CASE WHEN nai.project_monomer_id != '4d10c10a6b292568ee32642e364a3022' THEN 1 ELSE 0 END) as new_project_mismatch,
    SUM(CASE WHEN oai.project_monomer_id != '4d10c10a6b292568ee32642e364a3022' THEN 1 ELSE 0 END) as old_project_mismatch,
    SUM(CASE WHEN nai.archives_type != oai.archives_type THEN 1 ELSE 0 END) as type_mismatch,
    COUNT(*) as total_temp_records
FROM temp_matched_archives tma
LEFT JOIN dgdoc_archives_instance nai ON tma.new_archives_id = nai.archives_id
LEFT JOIN dgdoc_archives_instance_old oai ON tma.old_archives_id = oai.archives_id;
