-- 修复后的重复档案查询
-- 统一根节点判断条件：LENGTH(parent_id) = 1

-- 先运行这个调试查询，确认数据存在
-- SELECT archives_id, parent_id, archives_name, LENGTH(parent_id) as parent_len 
-- FROM dgdoc_archives 
-- WHERE is_sz = 0 AND archives_type = '1' 
-- AND (archives_name LIKE '%基坑支护%' OR archives_name LIKE '%桩基%')
-- LIMIT 10;

WITH RECURSIVE
-- 新档案树结构（从根节点开始递归）
new_tree AS (
    -- 基础查询：根节点（统一使用LENGTH = 1）
    SELECT
        da1.archives_id,
        da1.parent_id,
        da1.archives_name,
        da1.archives_type,
        da1.is_sz,
        1 AS level,
        CAST(da1.archives_name AS CHAR(1000)) AS path,
        CAST(da1.archives_id AS CHAR(1000)) AS id_path
    FROM
        dgdoc_archives da1
    WHERE
        LENGTH(da1.parent_id) = 1  -- 根节点parent_id长度为1
      AND da1.is_sz = 0
      AND da1.archives_type = '1'
      AND da1.archives_name IN (
                                '基坑支护子分部工程验收资料抽查表',
                                '桩基分项工程验收资料抽查表',
                                '地基与基础分部工程验收资料抽查表',
                                '建筑装饰装修分部工程验收资料抽查表',
                                '屋面分部工程验收资料抽查表',
                                '通风与空调分部工程验收资料抽查表',
                                '建筑电气分部工程验收资料抽查表',
                                '智能建筑分部工程验收资料抽查表'
        )

    UNION ALL

    -- 递归查询：子节点
    SELECT
        da2.archives_id,
        da2.parent_id,
        da2.archives_name,
        da2.archives_type,
        da2.is_sz,
        nt.level + 1,
        CONCAT(nt.path, ' -> ', da2.archives_name),
        CONCAT(nt.id_path, ',', da2.archives_id)
    FROM
        dgdoc_archives da2
    INNER JOIN new_tree nt ON da2.parent_id = nt.archives_id
    WHERE
        da2.is_sz = 0
      AND da2.archives_type = '1'
      AND nt.level < 10  -- 防止无限递归
),

-- 旧档案树结构（统一使用LENGTH = 1）
old_tree AS (
    -- 基础查询：根节点（修改为LENGTH = 1）
    SELECT
        da2.archives_id,
        da2.parent_id,
        da2.archives_name,
        da2.archives_type,
        da2.is_sz,
        1 AS level,
        CAST(da2.archives_name AS CHAR(1000)) AS path,
        CAST(da2.archives_id AS CHAR(1000)) AS id_path
    FROM
        dgdoc_archives da2
    WHERE
        LENGTH(da2.parent_id) = 1  -- 修改：统一使用LENGTH = 1
      AND da2.is_sz = 1
      AND da2.archives_type = '1'

    UNION ALL

    -- 递归查询：子节点
    SELECT
        da3.archives_id,
        da3.parent_id,
        da3.archives_name,
        da3.archives_type,
        da3.is_sz,
        ot.level + 1,
        CONCAT(ot.path, ' -> ', da3.archives_name),
        CONCAT(ot.id_path, ',', da3.archives_id)
    FROM
        dgdoc_archives da3
    INNER JOIN old_tree ot ON da3.parent_id = ot.archives_id
    WHERE
        da3.is_sz = 1
      AND da3.archives_type = '1'
      AND ot.level < 10  -- 防止无限递归
),

-- 查找新档案中的重复名称（同一根节点下）
new_duplicates AS (
    SELECT
        SUBSTRING_INDEX(id_path, ',', 1) AS root_id,
        archives_name,
        COUNT(*) AS duplicate_count
    FROM new_tree
    WHERE level > 1  -- 排除根节点
    GROUP BY SUBSTRING_INDEX(id_path, ',', 1), archives_name
    HAVING COUNT(*) > 1
),

-- 查找旧档案中对应的名称
matching_old_names AS (
    SELECT DISTINCT
        nd.root_id,
        nd.archives_name,
        nd.duplicate_count,
        ot.archives_name AS old_name,
        ot.archives_id AS old_id
    FROM new_duplicates nd
    JOIN old_tree ot ON nd.archives_name = ot.archives_name
    WHERE ot.level > 1  -- 排除根节点
)

-- 最终结果
SELECT
    nt_root.archives_name AS '新档案根节点',
    mon.archives_name AS '重复的档案名称',
    mon.duplicate_count AS '重复次数',
    GROUP_CONCAT(
        CONCAT('新档案ID: ', nt.archives_id, ' (路径: ', nt.path, ')')
        ORDER BY nt.archives_id
        SEPARATOR '\n'
    ) AS '新档案详情',
    GROUP_CONCAT(
        DISTINCT CONCAT('旧档案ID: ', mon.old_id)
        ORDER BY mon.old_id
        SEPARATOR '\n'
    ) AS '对应的旧档案'
FROM matching_old_names mon
JOIN new_tree nt ON SUBSTRING_INDEX(nt.id_path, ',', 1) = mon.root_id 
                 AND nt.archives_name = mon.archives_name
                 AND nt.level > 1
JOIN new_tree nt_root ON nt_root.archives_id = mon.root_id
GROUP BY mon.root_id, nt_root.archives_name, mon.archives_name, mon.duplicate_count
ORDER BY nt_root.archives_name, mon.duplicate_count DESC;
