package org.jeecg.modules.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.system.entity.SysFillRule;
import org.jeecg.modules.system.mapper.SysFillRuleMapper;
import org.jeecg.modules.system.service.ISysFillRuleService;
import org.springframework.stereotype.Service;

/**
 * @Description: 填值规则
 * @Author: jky
 * @Date: 2019-11-07
 * @Version: V1.0
 */
@Service("sysFillRuleServiceImpl")
public class SysFillRuleServiceImpl extends ServiceImpl<SysFillRuleMapper, SysFillRule> implements ISysFillRuleService {

}
