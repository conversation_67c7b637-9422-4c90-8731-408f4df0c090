package org.jeecg.modules.system.security;

import org.apache.shiro.web.filter.mgt.PathMatchingFilterChainResolver;
import org.springframework.stereotype.Component;

import javax.servlet.ServletRequest;

/**
 * <AUTHOR>
 * @project jky-boot
 * @description
 * @date 2023/11/23
 */
@Component
public class MyPathMatchingFilterChainResolver extends PathMatchingFilterChainResolver {


    public String  getMyPathWithinApplication(ServletRequest request){
        return super.getPathWithinApplication(request);
    }

    public boolean MyPathMatches2(String pattern, String path) {
        return super.pathMatches(pattern,path);
    }
}
