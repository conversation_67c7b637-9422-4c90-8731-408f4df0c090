//package org.jeecg.modules.system.service.impl;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
//import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
//import com.baomidou.mybatisplus.core.metadata.IPage;
//import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
//import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang.StringUtils;
//import org.apache.shiro.SecurityUtils;
//import org.jeecg.common.api.vo.Result;
//import org.jeecg.common.constant.CacheConstant;
//import org.jeecg.common.constant.CommonConstant;
//import org.jeecg.common.constant.DataBaseConstant;
//import org.jeecg.common.system.vo.LoginUser;
//import org.jeecg.common.util.PasswordUtil;
//import org.jeecg.common.util.oConvertUtils;
//import org.jeecg.modules.base.service.BaseCommonService;
//import org.jeecg.modules.system.entity.*;
//import org.jeecg.modules.system.mapper.*;
//import org.jeecg.modules.system.model.DepartIdModel;
//import org.jeecg.modules.system.service.ISysUserService2;
//import org.jeecg.modules.system.vo.SysUserDepVo;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.cache.annotation.CacheEvict;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//
//import java.util.*;
//import java.util.stream.Collectors;
//
//@Slf4j
//@Service
//public class ISysUserService2Impl extends ServiceImpl<SysUserMapper, SysUser> implements ISysUserService2 {
//
//    @Autowired(required = false)
//    private SysUserRoleMapper sysUserRoleMapper;
//
//    @Autowired(required = false)
//    private SysDepartMapper sysDepartMapper;
//
//    @Autowired(required = false)
//    private SysUserDepartMapper sysUserDepartMapper;
//
//    @Autowired(required = false)
//    private BaseCommonService baseCommonService;
//
//    @Autowired(required = false)
//    private SysDepartRoleMapper sysDepartRoleMapper;
//
//    @Autowired(required = false)
//    private SysDepartRoleUserMapper sysDepartRoleUserMapper;
//
//    @Autowired(required = false)
//    private SysUserMapper sysUserMapper;
//
//    @Override
//    public IPage<SysUser> findList(SysUser user, Integer pageNo, Integer pageSize) throws Exception {
//        QueryWrapper<SysUser> queryWrapper = new QueryWrapper<>();
//        //TODO 外部模拟登陆临时账号，列表不显示
//        if (user.getUsername() != null) {
//            queryWrapper.likeRight("username", user.getUsername());
//        }
//        if (user.getOrganizationId() != null) {
//            queryWrapper.likeRight("organization_id", user.getOrganizationId());
//        }
//        if (user.getPhone() != null) {
//            queryWrapper.likeRight("phone", user.getPhone());
//        }
//        if (user.getStatus() != null) {
//            queryWrapper.eq("status", user.getStatus());
//        }
//        Page<SysUser> page = new Page<SysUser>(pageNo, pageSize);
//        IPage<SysUser> pageList = this.page(page, queryWrapper);
//        //批量查询用户的所属部门
//        //step.1 先拿到全部的 useids
//        //step.2 通过 useids，一次性查询用户的所属部门名字
//        List<String> userIds = pageList.getRecords().stream().map(SysUser::getId).collect(Collectors.toList());
//        if (userIds != null && userIds.size() > 0) {
//            Map<String, String> useDepNames = this.getDepNamesByUserIds(userIds);
//            pageList.getRecords().forEach(item -> {
//                item.setOrgCodeTxt(useDepNames.get(item.getId()));
//            });
//        }
//        return pageList;
//    }
//
//    @Transactional
//    @Override
//    public SysUser add(SysUser user, String selectedRoles, String selectedDeparts) throws Exception {
//        // 判断必要参数是否存在
//        String organizationId = user.getOrganizationId();
//        String realname = user.getRealname();
//        if (StringUtils.isBlank(organizationId) && StringUtils.isBlank(realname)) {
//            throw new Exception("添加用户参数缺少");
//        }
//        // 如果密码为nul，设置默认密码
//        if (StringUtils.isBlank(user.getPassword())) {
//            user.setPassword("Dg@07690769");
//        }
//        // 设置组织机构代码为账户
//        user.setUsername(user.getOrganizationId());
//        user.setCreateTime(new Date());//设置创建时间
//        String salt = oConvertUtils.randomGen(8);
//        user.setSalt(salt);
//        String passwordEncode = PasswordUtil.encrypt(user.getUsername(), user.getPassword(), salt);
//        user.setPassword(passwordEncode);
//        user.setStatus(1);
//        user.setDelFlag(CommonConstant.DEL_FLAG_0);
//        // 查询账户是否重复
//        QueryWrapper<SysUser> queryWrapper = new QueryWrapper<>();
//        queryWrapper.eq("realname", user.getRealname()).or().eq("organization_id", user.getOrganizationId());
//        SysUser checkUser = this.getOne(queryWrapper);
//        if (checkUser != null) {
//            throw new Exception("该企业机构已经存在账户，无法创建账户！");
//        }
//        //step.1 保存用户
//        this.save(user);
//        //step.2 保存角色
//        if (oConvertUtils.isNotEmpty(selectedRoles)) {
//            String[] arr = selectedRoles.split(",");
//            for (String roleId : arr) {
//                SysUserRole userRole = new SysUserRole(user.getId(), roleId);
//                sysUserRoleMapper.insert(userRole);
//            }
//        }
//        //step.3 保存所属部门
//        SysDepart sysDepart = new SysDepart();
//        sysDepart.setDepartName(user.getRealname());
//        sysDepart.setOrganizationId(user.getOrganizationId());
//        sysDepart.setMail(user.getEmail());
//        sysDepart.setDelFlag("0");
//        sysDepart.setEntType(user.getEntType());
//        sysDepartMapper.insert(sysDepart);
//        // 追加账户与部门的关联表格
//        if (oConvertUtils.isNotEmpty(selectedDeparts)) {
//            String[] arr = selectedDeparts.split(",");
//            for (String deaprtId : arr) {
//                SysUserDepart userDeaprt = new SysUserDepart(user.getId(), deaprtId);
//                sysUserDepartMapper.insert(userDeaprt);
//            }
//        }
//        return user;
//    }
//
//    @Transactional
//    @Override
//    public Result<SysUser> edit(SysUser user, String selectedRoles, String selectedDeparts) throws Exception {
//        Result<SysUser> result = new Result<>();
//        SysUser sysUser = this.getById(user.getId());
//        baseCommonService.addLog("编辑用户，id： " + user.getId(), CommonConstant.LOG_TYPE_2, 2);
//        if (sysUser == null) {
//            result.error500("未找到对应实体");
//        } else {
//            user.setUpdateTime(new Date());
//            // 修改密码
//            String passwordEncode = PasswordUtil.encrypt(user.getUsername(), user.getPassword(), sysUser.getSalt());
//            user.setPassword(passwordEncode);
//            String roles = selectedRoles;
//            String departs = selectedDeparts;
//            if (oConvertUtils.isEmpty(departs)) {
//                //vue3.0前端只传递了departIds
//                departs = user.getDepartIds();
//            }
//            // 修改用户走一个service 保证事务
//            if (!DataBaseConstant.isSuperAdmin(user.getId())) {
//                this.editUser(user, roles, departs);
//                result.success("修改成功!");
//            }
//            LoginUser curUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
//            if (DataBaseConstant.SUPER_ADMIN_ID.equals(curUser.getId())) {
//                this.editUser(user, roles, departs);
//                result.success("修改成功!");
//            }
//            Result.error("不允许修改admin信息");
//        }
//        return result;
//    }
//
//    @Override
//    public SysUser findById(String id) throws Exception {
//        QueryWrapper<SysUser> queryWrapper = new QueryWrapper<>();
//        queryWrapper.eq("id", id);
//        SysUser one = this.getOne(queryWrapper);
//        return one;
//    }
//
//    /**
//     * 删除用户 通过id
//     *
//     * @param id
//     * @throws Exception
//     */
//    @Override
//    public void delById(String id) throws Exception {
//        this.removeById(id);
//    }
//
//
//    /**
//     * 删除用户 通过id列表
//     *
//     * @param ids
//     * @throws Exception
//     */
//    @Override
//    public void delByIds(List<String> ids) throws Exception {
//        this.removeByIds(ids);
//    }
//
//    @Override
//    public SysUser getUserByName(String username) {
//        return sysUserMapper.getUserByName(username);
//    }
//
//    /**
//     * 修改账户状态
//     *
//     * @param ids    用户id列表
//     * @param status 修改状态
//     * @throws Exception
//     */
//    @Transactional
//    @Override
//    public void updateStatus(String[] ids, String status) throws Exception {
//        for (String id : ids) {
//            if (oConvertUtils.isNotEmpty(id)) {
//                this.update(new SysUser().setStatus(Integer.parseInt(status)),
//                        new UpdateWrapper<SysUser>().lambda().eq(SysUser::getId, id));
//            }
//        }
//    }
//
//    @Override
//    @CacheEvict(value = {CacheConstant.SYS_USERS_CACHE}, allEntries = true)
//    public Result<?> changePassword(SysUser sysUser) {
//        String salt = oConvertUtils.randomGen(8);
//        sysUser.setSalt(salt);
//        String password = sysUser.getPassword();
//        String passwordEncode = PasswordUtil.encrypt(sysUser.getUsername(), password, salt);
//        sysUser.setPassword(passwordEncode);
//        sysUserMapper.updateById(sysUser);
//        return Result.ok("密码修改成功!");
//    }
//
//    @Override
//    public Map<String, String> getDepNamesByUserIds(List<String> userIds) {
//        List<SysUserDepVo> list = this.baseMapper.getDepNamesByUserIds(userIds);
//
//        Map<String, String> res = new HashMap<String, String>();
//        list.forEach(item -> {
//                    if (res.get(item.getUserId()) == null) {
//                        res.put(item.getUserId(), item.getDepartName());
//                    } else {
//                        res.put(item.getUserId(), res.get(item.getUserId()) + "," + item.getDepartName());
//                    }
//                }
//        );
//        return res;
//    }
//
//    /**
//     * 更新用户资料
//     *
//     * @param user
//     * @param roles
//     * @param departs
//     */
//    private void editUser(SysUser user, String roles, String departs) {
//        //step.1 修改用户基础信息
//        this.updateById(user);
//        //step.2 修改角色
//        //处理用户角色 先删后加
//        sysUserRoleMapper.delete(new QueryWrapper<SysUserRole>().lambda().eq(SysUserRole::getUserId, user.getId()));
//        if (oConvertUtils.isNotEmpty(roles)) {
//            String[] arr = roles.split(",");
//            for (String roleId : arr) {
//                SysUserRole userRole = new SysUserRole(user.getId(), roleId);
//                sysUserRoleMapper.insert(userRole);
//            }
//        }
//
//        //step.3 修改部门
//        String[] arr = {};
//        if (oConvertUtils.isNotEmpty(departs)) {
//            arr = departs.split(",");
//        }
//        //查询已关联部门
//        List<SysUserDepart> userDepartList = sysUserDepartMapper.selectList(new QueryWrapper<SysUserDepart>().lambda().eq(SysUserDepart::getUserId, user.getId()));
//        if (userDepartList != null && userDepartList.size() > 0) {
//            for (SysUserDepart depart : userDepartList) {
//                //修改已关联部门删除部门用户角色关系
//                if (!Arrays.asList(arr).contains(depart.getDepId())) {
//                    List<SysDepartRole> sysDepartRoleList = sysDepartRoleMapper.selectList(
//                            new QueryWrapper<SysDepartRole>().lambda().eq(SysDepartRole::getDepartId, depart.getDepId()));
//                    List<String> roleIds = sysDepartRoleList.stream().map(SysDepartRole::getId).collect(Collectors.toList());
//                    if (roleIds != null && roleIds.size() > 0) {
//                        sysDepartRoleUserMapper.delete(new QueryWrapper<SysDepartRoleUser>().lambda().eq(SysDepartRoleUser::getUserId, user.getId())
//                                .in(SysDepartRoleUser::getDroleId, roleIds));
//                    }
//                }
//            }
//        }
//        //先删后加
//        sysUserDepartMapper.delete(new QueryWrapper<SysUserDepart>().lambda().eq(SysUserDepart::getUserId, user.getId()));
//        if (oConvertUtils.isNotEmpty(departs)) {
//            for (String departId : arr) {
//                SysUserDepart userDepart = new SysUserDepart(user.getId(), departId);
//                sysUserDepartMapper.insert(userDepart);
//            }
//        }
//
//        //step.4 修改手机号和邮箱
//        // 更新手机号、邮箱空字符串为 null
//        sysUserMapper.updateNullByEmptyString("email");
//        sysUserMapper.updateNullByEmptyString("phone");
//    }
//
//    @Override
//    @CacheEvict(value = {CacheConstant.SYS_USERS_CACHE}, allEntries = true)
//    public Result<?> resetPassword(JSONObject json) {
//
//        String username = json.getString("username");
//        String oldpassword = json.getString("oldpassword");
//        String password = json.getString("password");
//        String confirmpassword = json.getString("confirmpassword");
//        LoginUser sysUser = (LoginUser)SecurityUtils.getSubject().getPrincipal();
//        if(!sysUser.getUsername().equals(username)){
//            return Result.error("只允许修改自己的密码！");
//        }
//        SysUser user = this.getOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getUsername, username));
//        if(user==null) {
//            return Result.error("用户不存在！");
//        }
//
//        SysUser user = sysUserMapper.getUserByName(username);
//        String passwordEncode = PasswordUtil.encrypt(username, oldpassword, user.getSalt());
//        if (!user.getPassword().equals(passwordEncode)) {
//            return Result.error("旧密码输入错误!");
//        }
//        if (oConvertUtils.isEmpty(newpassword)) {
//            return Result.error("新密码不允许为空!");
//        }
//        if (!newpassword.equals(confirmpassword)) {
//            return Result.error("两次输入密码不一致!");
//        }
//        String password = PasswordUtil.encrypt(username, newpassword, user.getSalt());
//        this.sysUserMapper.update(new SysUser().setPassword(password), new LambdaQueryWrapper<SysUser>().eq(SysUser::getId, user.getId()));
//        return Result.ok("密码重置成功!");
//    }
//
//}
