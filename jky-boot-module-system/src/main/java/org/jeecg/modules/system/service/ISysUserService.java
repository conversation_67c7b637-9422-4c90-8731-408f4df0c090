package org.jeecg.modules.system.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.vo.SysUserCacheInfo;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.model.SysUserSysDepartModel;
import org.jeecg.modules.system.vo.SysUserNameVo;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 用户表 服务类
 *
 * <AUTHOR>
 * @since 2022-03-20
 */
public interface ISysUserService extends IService<SysUser> {

    /**
     * 重置密码
     *
     * @param username
     * @param oldpassword
     * @param newpassword
     * @param confirmpassword
     * @return
     */
    public Result<?> resetPassword(String username, String oldpassword, String newpassword, String confirmpassword);

    /**
     * 修改密码
     *
     * @param sysUser
     * @return
     */
    public Result<?> changePassword(SysUser sysUser);

    /**
     * 删除用户
     *
     * @param userId
     * @return
     */
    public boolean deleteUser(String userId);

    /**
     * 批量删除用户
     *
     * @param userIds
     * @return
     */
    public boolean deleteBatchUsers(String userIds);

    public SysUser getUserByName(String username);

    /**
     * 添加用户和用户角色关系
     *
     * @param user
     * @param roles
     */
    public void addUserWithRole(SysUser user, String roles);


    /**
     * 修改用户和用户角色关系
     *
     * @param user
     * @param roles
     */
    public void editUserWithRole(SysUser user, String roles);

    /**
     * 获取用户的授权角色
     *
     * @param username
     * @return
     */
    public List<String> getRole(String username);

    /**
     * 查询用户信息包括 部门信息
     *
     * @param username
     * @return
     */
    public SysUserCacheInfo getCacheUser(String username);

    /**
     * 根据部门Id查询
     *
     * @param
     * @return
     */
    public IPage<SysUser> getUserByDepId(Page<SysUser> page, String departId, String username);

    /**
     * 根据部门Ids查询
     *
     * @param
     * @return
     */
    public IPage<SysUser> getUserByDepIds(Page<SysUser> page, List<String> departIds, String username);


    /**
     * 根据部门Ids查询
     *
     * @param
     * @return
     */
    public IPage<SysUser> getUsersByDepIds(Page<SysUser> page, List<String> departIds);

    /**
     * 根据 userIds查询，查询用户所属部门的名称（多个部门名逗号隔开）
     *
     * @param
     * @return
     */
    public Map<String, String> getDepNamesByUserIds(List<String> userIds);

    /**
     * 根据部门 Id 和 QueryWrapper 查询
     *
     * @param page
     * @param departId
     * @param queryWrapper
     * @return
     */
    public IPage<SysUser> getUserByDepartIdAndQueryWrapper(Page<SysUser> page, String departId, QueryWrapper<SysUser> queryWrapper);

    /**
     * 根据 orgCode 查询用户，包括子部门下的用户
     *
     * @param orgCode
     * @param userParams 用户查询条件，可为空
     * @param page       分页参数
     * @return
     */
    IPage<SysUserSysDepartModel> queryUserByOrgCode(String orgCode, SysUser userParams, IPage page);

    /**
     * 根据角色Id查询
     *
     * @param
     * @return
     */
    public IPage<SysUser> getUserByRoleId(Page<SysUser> page, String roleId, String username);

    /**
     * 通过用户名获取用户角色集合
     *
     * @param username 用户名
     * @return 角色集合
     */
    Set<String> getUserRolesSet(String username);

    /**
     * 通过用户名获取用户权限集合
     *
     * @param username 用户名
     * @return 权限集合
     */
    Set<String> getUserPermissionsSet(String username);

    /**
     * 根据用户名设置部门ID
     *
     * @param username
     * @param orgCode
     */
    void updateUserDepart(String username, String orgCode);

    /**
     * 根据手机号获取用户名和密码
     */
    public SysUser getUserByPhone(String phone);


    /**
     * 根据邮箱获取用户
     */
    public SysUser getUserByEmail(String email);


    /**
     * 添加用户和用户部门关系
     *
     * @param user
     * @param selectedParts
     */
    void addUserWithDepart(SysUser user, String selectedParts);

    /**
     * 编辑用户和用户部门关系
     *
     * @param user
     * @param departs
     */
    void editUserWithDepart(SysUser user, String departs);

    /**
     * 校验用户是否有效
     *
     * @param sysUser
     * @return
     */
    Result checkUserIsEffective(SysUser sysUser);

    /**
     * 查询被逻辑删除的用户
     */
    List<SysUser> queryLogicDeleted();

    /**
     * 查询被逻辑删除的用户（可拼装查询条件）
     */
    List<SysUser> queryLogicDeleted(LambdaQueryWrapper<SysUser> wrapper);

    /**
     * 还原被逻辑删除的用户
     */
    boolean revertLogicDeleted(List<String> userIds, SysUser updateEntity);

    /**
     * 彻底删除被逻辑删除的用户
     */
    boolean removeLogicDeleted(List<String> userIds);

    /**
     * 更新手机号、邮箱空字符串为 null
     */
    @Transactional(rollbackFor = Exception.class)
    boolean updateNullPhoneEmail();

    /**
     * 保存第三方用户信息
     *
     * @param sysUser
     */
    void saveThirdUser(SysUser sysUser);

    /**
     * 根据部门Ids查询
     *
     * @param
     * @return
     */
    List<SysUser> queryByDepIds(List<String> departIds, String username);

    /**
     * 保存用户
     *
     * @param user            用户
     * @param selectedRoles   选择的角色id，多个以逗号隔开
     * @param selectedDeparts 选择的部门id，多个以逗号隔开
     */
    void saveUser(SysUser user, String selectedRoles, String selectedDeparts) throws Exception;


    /**
     * 保存个人用户
     *
     * @param user            用户
     * @param selectedRoles   选择的角色id，多个以逗号隔开
     * @param selectedDeparts 选择的部门id，多个以逗号隔开
     */
    void savePersonUser(SysUser user, String selectedRoles, String selectedDeparts) throws Exception;

    /**
     * 编辑用户
     *
     * @param user    用户
     * @param roles   选择的角色id，多个以逗号隔开
     * @param departs 选择的部门id，多个以逗号隔开
     */
    void editUser(SysUser user, String roles, String departs);

    /**
     * userId转为username
     */
    List<String> userIdToUsername(Collection<String> userIdList);

    // add by jky 多个username获取realname
    List<SysUserNameVo> getRealNameByNames(List<String> userNames);

    SysUserNameVo getRealNameByName(String userName);


    /**
     * 添加用户
     *
     * @param user 用户角色资料  必传字段{realname:"建科院(企业名称)" ,"organizationId":"911113213111（组织机构ID）","entType":"1（企业类型）"}
     * @return 返回结果
     * {
     * code: 0
     * message: "success"  message值：1、success 2、新增用户必要参数缺少 3、新增用户异常
     * result: null
     * success: true success 值：1、true（添加成功） 2、false（添加失败）
     * timestamp: 1703664882226 （当前时间戳）
     * }
     * @throws Exception
     */
    Result<String> add(SysUser user) throws Exception;

}
