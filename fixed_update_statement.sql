-- 修复后的UPDATE语句及验证步骤

-- ========================================
-- 步骤1：验证匹配数据（执行UPDATE前必须运行）
-- ========================================

-- 检查能够匹配的记录数量
SELECT 
    '可匹配记录数' as info,
    COUNT(*) as count
FROM temp_matched_archives tma
INNER JOIN dgdoc_archives_instance nai ON tma.new_archives_id = nai.archives_id
INNER JOIN dgdoc_archives_instance_old oai ON tma.old_archives_id = oai.archives_id
WHERE nai.project_monomer_id = '4d10c10a6b292568ee32642e364a3022' 
  AND oai.project_monomer_id = '4d10c10a6b292568ee32642e364a3022';

-- 查看前5条匹配的数据详情
SELECT 
    tma.new_archives_id,
    tma.old_archives_id,
    nai.archives_name as new_name,
    oai.archives_name as old_name,
    nai.archives_type as new_type,
    oai.archives_type as old_type,
    nai.upload_state as current_upload_state,
    oai.upload_state as old_upload_state
FROM temp_matched_archives tma
INNER JOIN dgdoc_archives_instance nai ON tma.new_archives_id = nai.archives_id
INNER JOIN dgdoc_archives_instance_old oai ON tma.old_archives_id = oai.archives_id
WHERE nai.project_monomer_id = '4d10c10a6b292568ee32642e364a3022' 
  AND oai.project_monomer_id = '4d10c10a6b292568ee32642e364a3022'
LIMIT 5;

-- ========================================
-- 步骤2：修复版本1 - 移除archives_type条件
-- ========================================

-- 如果上面的验证显示有匹配记录，执行这个UPDATE
UPDATE temp_matched_archives tma
    INNER JOIN dgdoc_archives_instance nai ON tma.new_archives_id = nai.archives_id
    INNER JOIN dgdoc_archives_instance_old oai ON tma.old_archives_id = oai.archives_id
SET
    nai.file_type = oai.file_type,
    nai.file_url = oai.file_url,
    nai.file_size = oai.file_size,
    nai.file_page = oai.file_page,
    nai.upload_date = oai.upload_date,
    nai.upload_state = oai.upload_state,
    nai.audit_state = oai.audit_state,
    nai.status = oai.status,
    nai.return_back_reason = oai.return_back_reason,
    nai.return_back_remark = oai.return_back_remark,
    nai.cj_ent_confirm_file = oai.cj_ent_confirm_file,
    nai.create_by = oai.create_by,
    nai.update_by = '20250821数据更新',
    nai.update_time = NOW(),
    nai.create_time = oai.create_time
WHERE
    nai.project_monomer_id = '4d10c10a6b292568ee32642e364a3022' 
    AND oai.project_monomer_id = '4d10c10a6b292568ee32642e364a3022';
    -- 移除了 AND nai.archives_type = oai.archives_type 条件

-- ========================================
-- 步骤3：修复版本2 - 只通过临时表匹配
-- ========================================

-- 如果版本1还是不行，尝试这个更简单的版本
UPDATE dgdoc_archives_instance nai
    INNER JOIN temp_matched_archives tma ON nai.archives_id = tma.new_archives_id
    INNER JOIN dgdoc_archives_instance_old oai ON oai.archives_id = tma.old_archives_id
SET
    nai.file_type = oai.file_type,
    nai.file_url = oai.file_url,
    nai.file_size = oai.file_size,
    nai.file_page = oai.file_page,
    nai.upload_date = oai.upload_date,
    nai.upload_state = oai.upload_state,
    nai.audit_state = oai.audit_state,
    nai.status = oai.status,
    nai.return_back_reason = oai.return_back_reason,
    nai.return_back_remark = oai.return_back_remark,
    nai.cj_ent_confirm_file = oai.cj_ent_confirm_file,
    nai.create_by = oai.create_by,
    nai.update_by = '20250821数据更新',
    nai.update_time = NOW(),
    nai.create_time = oai.create_time
WHERE
    nai.project_monomer_id = '4d10c10a6b292568ee32642e364a3022' 
    AND oai.project_monomer_id = '4d10c10a6b292568ee32642e364a3022';

-- ========================================
-- 步骤4：修复版本3 - 分批更新
-- ========================================

-- 如果数据量很大，可以分批更新
UPDATE dgdoc_archives_instance nai
    INNER JOIN temp_matched_archives tma ON nai.archives_id = tma.new_archives_id
    INNER JOIN dgdoc_archives_instance_old oai ON oai.archives_id = tma.old_archives_id
SET
    nai.file_type = oai.file_type,
    nai.file_url = oai.file_url,
    nai.file_size = oai.file_size,
    nai.file_page = oai.file_page,
    nai.upload_date = oai.upload_date,
    nai.upload_state = oai.upload_state,
    nai.audit_state = oai.audit_state,
    nai.status = oai.status,
    nai.return_back_reason = oai.return_back_reason,
    nai.return_back_remark = oai.return_back_remark,
    nai.cj_ent_confirm_file = oai.cj_ent_confirm_file,
    nai.create_by = oai.create_by,
    nai.update_by = '20250821数据更新',
    nai.update_time = NOW(),
    nai.create_time = oai.create_time
WHERE
    nai.project_monomer_id = '4d10c10a6b292568ee32642e364a3022' 
    AND oai.project_monomer_id = '4d10c10a6b292568ee32642e364a3022'
LIMIT 1000;  -- 每次更新1000条

-- ========================================
-- 步骤5：验证更新结果
-- ========================================

-- 检查更新了多少条记录
SELECT ROW_COUNT() as updated_rows;

-- 验证更新是否成功
SELECT 
    COUNT(*) as updated_records,
    COUNT(CASE WHEN nai.update_by = '20250821数据更新' THEN 1 END) as confirmed_updates
FROM temp_matched_archives tma
INNER JOIN dgdoc_archives_instance nai ON tma.new_archives_id = nai.archives_id
WHERE nai.project_monomer_id = '4d10c10a6b292568ee32642e364a3022';

-- 查看更新后的数据样例
SELECT 
    nai.archives_id,
    nai.archives_name,
    nai.file_url,
    nai.upload_state,
    nai.update_by,
    nai.update_time
FROM temp_matched_archives tma
INNER JOIN dgdoc_archives_instance nai ON tma.new_archives_id = nai.archives_id
WHERE nai.project_monomer_id = '4d10c10a6b292568ee32642e364a3022'
  AND nai.update_by = '20250821数据更新'
LIMIT 5;

-- ========================================
-- 步骤6：如果还是不行，尝试最简单的直接匹配
-- ========================================

-- 最后的备用方案：直接通过档案名称匹配
UPDATE dgdoc_archives_instance nai
    INNER JOIN dgdoc_archives_instance_old oai ON nai.archives_name = oai.archives_name
SET
    nai.file_type = oai.file_type,
    nai.file_url = oai.file_url,
    nai.file_size = oai.file_size,
    nai.file_page = oai.file_page,
    nai.upload_date = oai.upload_date,
    nai.upload_state = oai.upload_state,
    nai.audit_state = oai.audit_state,
    nai.status = oai.status,
    nai.return_back_reason = oai.return_back_reason,
    nai.return_back_remark = oai.return_back_remark,
    nai.cj_ent_confirm_file = oai.cj_ent_confirm_file,
    nai.create_by = oai.create_by,
    nai.update_by = '20250821数据更新_直接匹配',
    nai.update_time = NOW(),
    nai.create_time = oai.create_time
WHERE
    nai.project_monomer_id = '4d10c10a6b292568ee32642e364a3022' 
    AND oai.project_monomer_id = '4d10c10a6b292568ee32642e364a3022'
    AND nai.archives_type = oai.archives_type;
