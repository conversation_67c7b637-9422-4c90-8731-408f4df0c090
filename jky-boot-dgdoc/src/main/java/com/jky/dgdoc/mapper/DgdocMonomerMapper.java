package com.jky.dgdoc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jky.dgdoc.domain.PdMonomerCollect;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface DgdocMonomerMapper extends BaseMapper<PdMonomerCollect> {
    @Select("select dm.* from dgdoc_project dp inner join dgdoc_monomer dm on dp.project_id = dm.project_id   where dp.is_sz = '0';")
    List<PdMonomerCollect> selectNoSz();
}
