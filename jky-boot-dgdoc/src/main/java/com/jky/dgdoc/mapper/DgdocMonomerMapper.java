package com.jky.dgdoc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jky.dgdoc.domain.PdMonomerCollect;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Map;

public interface DgdocMonomerMapper extends BaseMapper<PdMonomerCollect> {
    @Select("select dm.* from dgdoc_project dp inner join dgdoc_monomer dm on dp.project_id = dm.project_id   where dp.is_sz = '0'")
    List<PdMonomerCollect> selectNoSz();

    @Select("SELECT distinct  project_monomer_id from  dgdoc_archives_instance_old  where file_url is not null and archives_type = '1'")
    List<String> selectOldData();


    @Select("select * from temp_matched_archives")
    List<Map<String,String>> selectTempMatchedArchives();

    @Update("UPDATE temp_matched_archives tma\n" +
            "    inner join dgdoc_archives_instance nai  on tma.new_archives_id = nai.archives_id\n" +
            "    INNER JOIN dgdoc_archives_instance_old oai ON  oai.archives_id  = tma.old_archives_id\n" +
            "SET\n" +
            "    nai.file_type = oai.file_type,\n" +
            "    nai.file_url = oai.file_url,\n" +
            "    nai.file_size = oai.file_size,\n" +
            "    nai.file_page = oai.file_page,\n" +
            "    nai.upload_date = oai.upload_date,\n" +
            "    nai.upload_state = oai.upload_state,\n" +
            "    nai.audit_state = oai.audit_state,\n" +
            "    nai.status = oai.status,\n" +
            "    nai.return_back_reason = oai.return_back_reason,\n" +
            "    nai.return_back_remark = oai.return_back_remark,\n" +
            "    nai.cj_ent_confirm_file = oai.cj_ent_confirm_file,\n" +
            "    nai.create_by = oai.create_by,\n" +
            "    nai.update_by = '20250821数据更新',\n" +
            "    nai.update_time = NOW(),\n" +
            "    nai.create_time = oai.create_time\n" +
            "    where\n" +
            "    nai.project_monomer_id = #{monomerId} AND\n" +
            "    oai.project_monomer_id = #{monomerId} AND\n" +
            "    nai.archives_type = '1' AND\n" +
            "    oai.archives_type = '1'  and\n" +
            "    tma.old_archives_id = #{oldArchivesId} and tma.new_archives_id = #{newArchivesId};")
    void updateNewData(String monomerId, String oldArchivesId, String newArchivesId);
}
