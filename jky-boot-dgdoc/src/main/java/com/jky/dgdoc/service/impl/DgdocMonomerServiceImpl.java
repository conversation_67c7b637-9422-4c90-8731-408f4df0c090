package com.jky.dgdoc.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jky.dgdoc.domain.DgdocArchives;
import com.jky.dgdoc.domain.DgdocArchivesInstance;
import com.jky.dgdoc.domain.PdEntInfoCollect;
import com.jky.dgdoc.domain.PdMonomerCollect;
import com.jky.dgdoc.domain.PdMonomerEntCollect;
import com.jky.dgdoc.domain.PdProjectCollect;
import com.jky.dgdoc.domain.PdProjectEntCollect;
import com.jky.dgdoc.domain.Rs;
import com.jky.dgdoc.domain.bo.MonomerBo;
import com.jky.dgdoc.domain.dto.SysUserDto;
import com.jky.dgdoc.domain.query.MonomerEntQuery;
import com.jky.dgdoc.domain.query.MonomerQuery;
import com.jky.dgdoc.domain.vo.DgzjjzjJcjgCoreSampleTableVo;
import com.jky.dgdoc.domain.vo.DgzjjzjSupJcjgUnqualifiedReplyFileVo;
import com.jky.dgdoc.domain.vo.MonomerVo;
import com.jky.dgdoc.domain.vo.PdMonomerCollectTableVo;
import com.jky.dgdoc.enums.ArchivesType;
import com.jky.dgdoc.enums.EntType;
import com.jky.dgdoc.enums.MonomerDataSource;
import com.jky.dgdoc.enums.UploadState;
import com.jky.dgdoc.fegin.DgdocApiClient;
import com.jky.dgdoc.mapper.DgdocMonomerEntMapper;
import com.jky.dgdoc.mapper.DgdocMonomerMapper;
import com.jky.dgdoc.mapper.DgdocProjectEntMapper;
import com.jky.dgdoc.mapper.DgdocProjectMapper;
import com.jky.dgdoc.mapper.DgzjjzjJcjgCoreSampleMapper;
import com.jky.dgdoc.service.IDgdocArchivesInstanceService;
import com.jky.dgdoc.service.IDgdocArchivesService;
import com.jky.dgdoc.service.IDgdocMonomerService;
import com.jky.dgdoc.service.IDgdocProjectService;
import com.jky.modules.estar.nd.exception.EstarException;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.util.UUIDGenerator;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: lpg
 * @Date: 2024/01/02/16:49
 * @Description:
 */
@Service
@RequiredArgsConstructor
public class DgdocMonomerServiceImpl extends ServiceImpl<DgdocMonomerMapper, PdMonomerCollect> implements IDgdocMonomerService {

    private final DgdocApiClient dgDocApiClient;
    private final IDgdocProjectService projectService;
    private final DgdocMonomerEntMapper dgdocMonomerEntMapper;
    private final DgdocProjectEntMapper projectEntMapper;
    private final DgdocProjectMapper projectMapper;
    private final IDgdocArchivesService archivesService;
    private final IDgdocArchivesInstanceService archivesInstanceService;
    private final DgzjjzjJcjgCoreSampleMapper dgzjjzjJcjgCoreSampleMapper;
    private final PlatformTransactionManager platformTransactionManager;
    private final DgdocMonomerMapper monomerMapper;


    @Override
    public List<DgzjjzjJcjgCoreSampleTableVo> queryReportById(String monomerId) {
        List<DgzjjzjJcjgCoreSampleTableVo> dgzjjzjJcjgCoreSampleTableVos = dgzjjzjJcjgCoreSampleMapper.queryMonomerJcjgList(monomerId);
        for (DgzjjzjJcjgCoreSampleTableVo dgzjjzjJcjgCoreSampleTableVo : dgzjjzjJcjgCoreSampleTableVos) {
            if (StringUtils.isNotBlank(dgzjjzjJcjgCoreSampleTableVo.getSynum())) {
                //查询不合格销案情况
                List<DgzjjzjSupJcjgUnqualifiedReplyFileVo> dgzjjzjSupJcjgUnqualifiedReplyFileVos = dgzjjzjJcjgCoreSampleMapper.queryMonomerJcjgByJcbh(dgzjjzjJcjgCoreSampleTableVo.getSynum());
                dgzjjzjJcjgCoreSampleTableVo.setSupDealProcess(dgzjjzjSupJcjgUnqualifiedReplyFileVos);
            }
        }
        return dgzjjzjJcjgCoreSampleTableVos;
    }

    @Override
    public void dataSync() {
        //查询非市政项目的单体插入数据
        List<PdMonomerCollect> pdMonomerCollects = monomerMapper.selectNoSz();
        for (PdMonomerCollect pdMonomerCollect : pdMonomerCollects) {
            List<DgdocArchives> monomerArchives =
                        archivesService.selectArchivesListByTypeAndIsSz(ArchivesType.MONOMER.getCode(), false);
            List<DgdocArchivesInstance> archivesInstances = BeanUtil.copyToList(monomerArchives, DgdocArchivesInstance.class);
            archivesInstances.forEach(a -> {
                //设置单体档案
                a.setArchivesType(ArchivesType.MONOMER.getCode());
                //设置单体ID
                a.setProjectMonomerId(pdMonomerCollect.getMonomerId());
            });
            archivesInstanceService.saveBatch(archivesInstances);
        }
    }

    @Override
    public List<PdMonomerCollectTableVo> queryList(String projectId) {
        Assert.notNull(projectId, "项目ID不能为空");

        LambdaQueryWrapper<PdMonomerCollect> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PdMonomerCollect::getProjectId, projectId);
        queryWrapper.orderByDesc(PdMonomerCollect::getCreateTime);

        List<PdMonomerCollect> monomerList = baseMapper.selectList(queryWrapper);
        List<PdMonomerCollectTableVo> pdMonomerCollects = BeanUtil.copyToList(monomerList, PdMonomerCollectTableVo.class);


        for (PdMonomerCollectTableVo pdMonomerCollect : pdMonomerCollects) {
            //查询单体参建单位信息
            List<PdMonomerEntCollect> pdMonomerEntCollects = dgdocMonomerEntMapper.selectList(Wrappers.<PdMonomerEntCollect>lambdaQuery().eq(PdMonomerEntCollect::getMonomerId, pdMonomerCollect.getMonomerId()));

            Map<String, PdMonomerEntCollect> entCollectMap = pdMonomerEntCollects.stream().collect(Collectors.toMap(PdMonomerEntCollect::getEntType, Function.identity()));

            pdMonomerCollect.setSgUnit(entCollectMap.get(EntType.CONSTRUCTION.getCode()));
            pdMonomerCollect.setSjUnit(entCollectMap.get(EntType.DESIGN.getCode()));
            pdMonomerCollect.setJlUnit(entCollectMap.get(EntType.SUPERVISION.getCode()));
            pdMonomerCollect.setKcUnit(entCollectMap.get(EntType.SURVEY.getCode()));

            //查询项目名称
            PdProjectCollect project = projectService.getById(pdMonomerCollect.getProjectId());
            pdMonomerCollect.setProjectName(project.getProjectName());
        }
        return pdMonomerCollects;
    }

    @Override
    public MonomerVo queryById(String monomerId) {
        MonomerVo monomerVo = new MonomerVo();
        PdMonomerCollect monomerCollect = this.getById(monomerId);
        Assert.notNull(monomerCollect, "查询不到单体信息");

        BeanUtil.copyProperties(monomerCollect, monomerVo);

        //查询单体参建单位信息
        List<PdMonomerEntCollect> pdMonomerEntCollects = dgdocMonomerEntMapper.selectList(Wrappers.<PdMonomerEntCollect>lambdaQuery().eq(PdMonomerEntCollect::getMonomerId, monomerId));

        Map<String, PdMonomerEntCollect> entCollectMap = pdMonomerEntCollects.stream().collect(Collectors.toMap(PdMonomerEntCollect::getEntType, Function.identity()));

        monomerVo.setSgUnit(entCollectMap.get(EntType.CONSTRUCTION.getCode()));
        monomerVo.setSjUnit(entCollectMap.get(EntType.DESIGN.getCode()));
        monomerVo.setJlUnit(entCollectMap.get(EntType.SUPERVISION.getCode()));
        monomerVo.setKcUnit(entCollectMap.get(EntType.SURVEY.getCode()));

        //查询项目名称
        PdProjectCollect project = projectService.getById(monomerCollect.getProjectId());
        monomerVo.setProjectName(project.getProjectName());


        return monomerVo;
    }

    @Override
    public Result<Rs<PdMonomerCollect>> queryMonomerList(MonomerQuery query) {
        PdMonomerCollect monomerCollect = BeanUtil.toBean(query, PdMonomerCollect.class);
        Result<Rs<PdMonomerCollect>> result = dgDocApiClient.getMonomerCollect(monomerCollect);
        PdProjectCollect project = projectService.getById(query.getProjectId());
        result.getResult().getList().forEach(a -> {
            //设置项目名称
            a.setProjectName(project.getProjectName());

            //不需要施工表查询了，在查单体时已完善以下信息 20210320 by lixh
//            if (StringUtils.isNotBlank(a.getDataId())) {
//                //查询施工许可证信息
//                PdConsPermitCollect pdConsPermitCollect = new PdConsPermitCollect();
//                pdConsPermitCollect.setConsPermitId(a.getDataId());
//
//                Result<Rs<PdConsPermitCollect>> permitCollect = dgDocApiClient.getConsPermitCollect(pdConsPermitCollect);
//                if (permitCollect.isSuccess() && !permitCollect.getResult().getList().isEmpty()) {
//                    PdConsPermitCollect consPermitCollect = permitCollect.getResult().getList().get(0);
//                    //计划开始时间
//                    a.setPlanStartDate(consPermitCollect.getStartdate());
//                    //计划结束时间
//                    a.setPlanEndDate(consPermitCollect.getEnddate());
//                    //施工许可证url
//                    a.setLicenceFile(consPermitCollect.getConsFileUrl());
//                    //建设工程规划许可证证号
//                    a.setGcghxkNo(consPermitCollect.getGcghxkNo());
//                }
//            }

        });
        List<PdMonomerCollect> pdMonomerCollects = result.getResult().getList();
        //如果是房地产项目，且单体信息为空，则构建虚拟单体信息
        if (project.getIsSz() && pdMonomerCollects.isEmpty()) {
            PdMonomerCollect monomer = BeanUtil.toBean(monomerCollect, PdMonomerCollect.class);
            monomer.setProjectId(query.getProjectId());
            monomer.setProjectName(project.getProjectName());
            monomer.setMonomerName(project.getProjectName());
            monomer.setMonomerId(UUIDGenerator.generate());
            monomer.setDataSource(MonomerDataSource.GENERATED_BY_PROJECT.getCode());
            monomer.setIsVirtual(true);
            pdMonomerCollects.add(monomer);
            return result;
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean remove(List<String> monomerIds) {
        List<PdMonomerCollect> monomerCollects = baseMapper.selectBatchIds(monomerIds);

        for (PdMonomerCollect monomerCollect : monomerCollects) {
            //查询单体档案信息
            List<DgdocArchivesInstance> archivesInstances = archivesInstanceService.list(Wrappers.<DgdocArchivesInstance>lambdaQuery()
                    .eq(DgdocArchivesInstance::getProjectMonomerId, monomerCollect.getMonomerId()));

            //如果存在上传的单体档案信息，则不允许删除
            boolean allMatch = archivesInstances.stream().allMatch(a -> a.getUploadState().equals(UploadState.UNUPLOAD.getCode()));
            if (!allMatch) {
                throw new JeecgBootException("单体下存在已上传的档案信息，不允许删除");
            }
            //删除单体档案
            archivesInstanceService.removeByIds(archivesInstances);
            //删除单体参建单位信息
            List<PdMonomerEntCollect> pdMonomerEntCollects = dgdocMonomerEntMapper.selectList(Wrappers.<PdMonomerEntCollect>lambdaQuery().eq(PdMonomerEntCollect::getMonomerId, monomerCollect.getMonomerId()));

            if (!pdMonomerEntCollects.isEmpty()) {
                List<String> entIds = pdMonomerEntCollects.stream().map(PdMonomerEntCollect::getEntId).collect(Collectors.toList());
                //删除项目参建单位信息
                projectEntMapper.delete(Wrappers.<PdProjectEntCollect>lambdaQuery().in(PdProjectEntCollect::getEntId, entIds));
                //删除单体参建单位信息
                dgdocMonomerEntMapper.delete(Wrappers.<PdMonomerEntCollect>lambdaQuery().in(PdMonomerEntCollect::getEntId, entIds).eq(PdMonomerEntCollect::getMonomerId, monomerCollect.getMonomerId()));
            }

        }

        return this.removeByIds(new ArrayList<>(monomerIds));
    }

    @SneakyThrows
    @Override
    @Transactional
    public Boolean update(MonomerBo bo) {
        PdMonomerCollect pdMonomerCollect = BeanUtil.toBean(bo, PdMonomerCollect.class);
        saveBeforeValidated(bo);
        //保存单体参建单位信息
        this.saveMonomerUnitInfo(bo);
        return this.updateById(pdMonomerCollect);
    }

    @Override
    public Result<Rs<PdMonomerEntCollect>> queryEntList(MonomerEntQuery query) {
        PdMonomerEntCollect monomerEntCollect = BeanUtil.toBean(query, PdMonomerEntCollect.class);

        PdProjectCollect pdProjectCollect = projectMapper.selectById(query.getProjectId());
        Result<Rs<PdMonomerEntCollect>> result = dgDocApiClient.getMonomerEntCollect(monomerEntCollect);
        result.getResult().getList().forEach(a -> {
            if (StringUtils.isBlank(a.getEntId())) {
                //查询参建单位信息
                PdEntInfoCollect pdEntInfoCollect = new PdEntInfoCollect();
                pdEntInfoCollect.setEntType(a.getEntType());
                pdEntInfoCollect.setEntName(a.getEntName());
                Result<Rs<PdEntInfoCollect>> entCollect = dgDocApiClient.getEntCollect(pdEntInfoCollect);
                if (entCollect.isSuccess() && !entCollect.getResult().getList().isEmpty()) {
                    a.setEntId(entCollect.getResult().getList().get(0).getEntId());
                }
            }
        });
        if (pdProjectCollect.getIsSz() && result.getResult().getList().isEmpty()) {
            //查询项目的参建单位信息
            List<PdProjectEntCollect> pdProjectEntCollects = projectEntMapper.selectList(Wrappers.<PdProjectEntCollect>lambdaQuery()
                    .eq(PdProjectEntCollect::getProjectId, query.getProjectId())
                    .eq(PdProjectEntCollect::getEntType, query.getEntType()));

            if (pdProjectEntCollects.isEmpty()) {
                return result;
            }
            List<PdMonomerEntCollect> pdMonomerEntCollects = BeanUtil.copyToList(pdProjectEntCollects, PdMonomerEntCollect.class);
            //设置单体ID
            pdMonomerEntCollects.forEach(a -> a.setMonomerId(query.getMonomerId()));

            Result<Rs<PdMonomerEntCollect>> rsResult = new Result<>();
            Rs<PdMonomerEntCollect> rs = new Rs<>();
            rs.setList(pdMonomerEntCollects);
            rsResult.setResult(rs);
            return rsResult;
        }
        List<PdMonomerEntCollect> filterList = result.getResult().getList().stream().filter(a -> StringUtils.isNotBlank(a.getEntId())).collect(Collectors.toList());
        result.getResult().setList(filterList);
        return result;
    }

    @Override
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public Boolean insert(MonomerBo bo) {
        PdMonomerCollect pdMonomerCollect = BeanUtil.toBean(bo, PdMonomerCollect.class);

        boolean exists = baseMapper.exists(Wrappers.<PdMonomerCollect>lambdaQuery().eq(PdMonomerCollect::getMonomerId, pdMonomerCollect.getMonomerId()));
        if (exists) {
            throw new EstarException("单体已存在");
        }

        saveBeforeValidated(bo);


        boolean save = this.save(pdMonomerCollect);
        if (save) {
            //保存单体参建单位信息
            this.saveMonomerUnitInfo(bo);
            saveArchivesInstance(pdMonomerCollect);
        }
        return save;
    }

    /**
     * 保存档案实例
     *
     * @param pdMonomerCollect
     */
    @Async("asyncTaskExecutor")
    public void saveArchivesInstance(PdMonomerCollect pdMonomerCollect) {
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        TransactionStatus transaction = platformTransactionManager.getTransaction(def);
        try {
            PdProjectCollect project = projectMapper.selectById(pdMonomerCollect.getProjectId());

            //保存单体档案
            List<DgdocArchives> monomerArchives;
            if (project.getIsSz()) {
                monomerArchives =
                        archivesService.selectArchivesListByTypeAndIsSz(ArchivesType.MONOMER.getCode(), true);
            } else {
                monomerArchives =
                        archivesService.selectArchivesListByTypeAndIsSz(ArchivesType.MONOMER.getCode(), false);
            }
            List<DgdocArchivesInstance> archivesInstances = BeanUtil.copyToList(monomerArchives, DgdocArchivesInstance.class);
            archivesInstances.forEach(a -> {
                //设置单体档案
                a.setArchivesType(ArchivesType.MONOMER.getCode());
                //设置单体ID
                a.setProjectMonomerId(pdMonomerCollect.getMonomerId());
            });
            archivesInstanceService.saveBatch(archivesInstances);

            //设置单体项目档案
            List<DgdocArchives> projectArchives;
            if (project.getIsSz()) {
                projectArchives =
                        archivesService.selectArchivesListByTypeAndIsSz(ArchivesType.PROJECT.getCode(), true);
            } else {
                projectArchives =
                        archivesService.selectArchivesListByTypeAndIsSz(ArchivesType.PROJECT.getCode(), false);
            }
            List<DgdocArchivesInstance> projectArchivesInstances = BeanUtil.copyToList(projectArchives, DgdocArchivesInstance.class);
            projectArchivesInstances.forEach(a -> {
                //设置项目档案
                a.setArchivesType(ArchivesType.PROJECT.getCode());
                //设置项目ID
                a.setProjectMonomerId(pdMonomerCollect.getMonomerId());
            });
            archivesInstanceService.saveBatch(projectArchivesInstances);

            platformTransactionManager.commit(transaction);
        } catch (Exception e) {
            platformTransactionManager.rollback(transaction);
            throw e;
        }

    }


    private static void saveBeforeValidated(MonomerBo bo) {
        //结束时间不能大于开始时间
        if (bo.getPlanStartDate() != null && bo.getPlanEndDate() != null && bo.getPlanEndDate().getTime() < bo.getPlanStartDate().getTime()) {
            throw new EstarException("计划完成时间不能大于计划开始时间");
        }
    }

    public void saveMonomerUnitInfo(MonomerBo bo) throws Exception {
        //删除单体参建单位信息
        dgdocMonomerEntMapper.delete(Wrappers.<PdMonomerEntCollect>lambdaQuery().eq(PdMonomerEntCollect::getMonomerId, bo.getMonomerId()));

        if (bo.getSgUnit() != null) {
            boolean sgUnitEx = dgdocMonomerEntMapper.exists(Wrappers.<PdMonomerEntCollect>lambdaQuery()
                    .eq(PdMonomerEntCollect::getProjectId, bo.getProjectId())
                    .eq(PdMonomerEntCollect::getMonomerId, bo.getMonomerId())
                    .eq(PdMonomerEntCollect::getEntType, EntType.CONSTRUCTION.getCode()));
            if (!sgUnitEx) {
                dgdocMonomerEntMapper.insert(bo.getSgUnit());
            }
            //查询关联项目的参建单位是否存在，不存在则新增
            boolean exists = projectEntMapper.exists(Wrappers.<PdProjectEntCollect>lambdaQuery().eq(PdProjectEntCollect::getProjectId, bo.getProjectId())
                    .eq(PdProjectEntCollect::getEntId, bo.getSgUnit().getEntId()));
            if (!exists) {
                PdProjectEntCollect projectEntCollect = BeanUtil.toBean(bo.getSgUnit(), PdProjectEntCollect.class);
                projectEntMapper.insert(projectEntCollect);
            }
            //添加用户
            this.addUser(bo.getSgUnit());
        }
        if (bo.getSjUnit() != null) {
            boolean sgUnitSj = dgdocMonomerEntMapper.exists(Wrappers.<PdMonomerEntCollect>lambdaQuery()
                    .eq(PdMonomerEntCollect::getProjectId, bo.getProjectId())
                    .eq(PdMonomerEntCollect::getMonomerId, bo.getMonomerId())
                    .eq(PdMonomerEntCollect::getEntType, EntType.DESIGN.getCode()));
            if (!sgUnitSj) {
                dgdocMonomerEntMapper.insert(bo.getSjUnit());
            }
            //查询关联项目的参建单位是否存在，不存在则新增
            boolean exists = projectEntMapper.exists(Wrappers.<PdProjectEntCollect>lambdaQuery().eq(PdProjectEntCollect::getProjectId, bo.getProjectId())
                    .eq(PdProjectEntCollect::getEntId, bo.getSjUnit().getEntId()));
            if (!exists) {
                PdProjectEntCollect projectEntCollect = BeanUtil.toBean(bo.getSjUnit(), PdProjectEntCollect.class);
                projectEntMapper.insert(projectEntCollect);
            }
            //添加用户
            this.addUser(bo.getSjUnit());
        }
        if (bo.getJlUnit() != null) {
            boolean sgUnitJl = dgdocMonomerEntMapper.exists(Wrappers.<PdMonomerEntCollect>lambdaQuery()
                    .eq(PdMonomerEntCollect::getProjectId, bo.getProjectId())
                    .eq(PdMonomerEntCollect::getMonomerId, bo.getMonomerId())
                    .eq(PdMonomerEntCollect::getEntType, EntType.SUPERVISION.getCode()));
            if (!sgUnitJl) {
                dgdocMonomerEntMapper.insert(bo.getJlUnit());
            }
            //查询关联项目的参建单位是否存在，不存在则新增
            boolean exists = projectEntMapper.exists(Wrappers.<PdProjectEntCollect>lambdaQuery().eq(PdProjectEntCollect::getProjectId, bo.getProjectId())
                    .eq(PdProjectEntCollect::getEntId, bo.getJlUnit().getEntId()));
            if (!exists) {
                PdProjectEntCollect projectEntCollect = BeanUtil.toBean(bo.getJlUnit(), PdProjectEntCollect.class);
                projectEntMapper.insert(projectEntCollect);
            }
            //添加用户
            this.addUser(bo.getJlUnit());
        }
        if (bo.getKcUnit() != null) {
            boolean sgUnitKc = dgdocMonomerEntMapper.exists(Wrappers.<PdMonomerEntCollect>lambdaQuery()
                    .eq(PdMonomerEntCollect::getProjectId, bo.getProjectId())
                    .eq(PdMonomerEntCollect::getMonomerId, bo.getMonomerId())
                    .eq(PdMonomerEntCollect::getEntType, EntType.SURVEY.getCode()));
            if (!sgUnitKc) {
                dgdocMonomerEntMapper.insert(bo.getKcUnit());
            }
            //查询关联项目的参建单位是否存在，不存在则新增
            boolean exists = projectEntMapper.exists(Wrappers.<PdProjectEntCollect>lambdaQuery().eq(PdProjectEntCollect::getProjectId, bo.getProjectId())
                    .eq(PdProjectEntCollect::getEntId, bo.getKcUnit().getEntId()));
            if (!exists) {
                PdProjectEntCollect projectEntCollect = BeanUtil.toBean(bo.getKcUnit(), PdProjectEntCollect.class);
                projectEntMapper.insert(projectEntCollect);
            }
            //添加用户
            this.addUser(bo.getKcUnit());
        }
    }

    /**
     * 添加用户
     *
     * @param bo
     */
    public void addUser(PdMonomerEntCollect bo) throws Exception {
        if (StringUtils.isBlank(bo.getEntId())) {
            return;
        }
        //添加用户信息
        SysUserDto sysUser = new SysUserDto();
        sysUser.setRealname(bo.getEntName());
        sysUser.setOrganizationId(bo.getCreditCode());
        sysUser.setEntType(bo.getEntType());
        Result<String> rs = dgDocApiClient.addDocEnt(sysUser);
        if (!rs.isSuccess()) {
            throw new EstarException("添加用户失败");
        }

    }
}
