package com.jky.dgdoc.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jky.dgdoc.domain.DgdocArchives;
import com.jky.dgdoc.domain.DgdocArchivesInstance;
import com.jky.dgdoc.domain.PdEntInfoCollect;
import com.jky.dgdoc.domain.PdMonomerCollect;
import com.jky.dgdoc.domain.PdMonomerEntCollect;
import com.jky.dgdoc.domain.PdProjectCollect;
import com.jky.dgdoc.domain.PdProjectEntCollect;
import com.jky.dgdoc.domain.Rs;
import com.jky.dgdoc.domain.bo.MonomerBo;
import com.jky.dgdoc.domain.dto.SysUserDto;
import com.jky.dgdoc.domain.query.MonomerEntQuery;
import com.jky.dgdoc.domain.query.MonomerQuery;
import com.jky.dgdoc.domain.vo.DgzjjzjJcjgCoreSampleTableVo;
import com.jky.dgdoc.domain.vo.DgzjjzjSupJcjgUnqualifiedReplyFileVo;
import com.jky.dgdoc.domain.vo.MonomerVo;
import com.jky.dgdoc.domain.vo.PdMonomerCollectTableVo;
import com.jky.dgdoc.enums.ArchivesType;
import com.jky.dgdoc.enums.EntType;
import com.jky.dgdoc.enums.MonomerDataSource;
import com.jky.dgdoc.enums.UploadState;
import com.jky.dgdoc.fegin.DgdocApiClient;
import com.jky.dgdoc.mapper.DgdocMonomerEntMapper;
import com.jky.dgdoc.mapper.DgdocMonomerMapper;
import com.jky.dgdoc.mapper.DgdocProjectEntMapper;
import com.jky.dgdoc.mapper.DgdocProjectMapper;
import com.jky.dgdoc.mapper.DgzjjzjJcjgCoreSampleMapper;
import com.jky.dgdoc.service.IDgdocArchivesInstanceService;
import com.jky.dgdoc.service.IDgdocArchivesService;
import com.jky.dgdoc.service.IDgdocMonomerService;
import com.jky.dgdoc.service.IDgdocProjectService;
import com.jky.modules.estar.nd.exception.EstarException;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.util.UUIDGenerator;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import java.io.FileWriter;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import org.springframework.util.IdGenerator;

/**
 * @Author: lpg
 * @Date: 2024/01/02/16:49
 * @Description:
 */
@Service
@RequiredArgsConstructor
public class DgdocMonomerServiceImpl extends ServiceImpl<DgdocMonomerMapper, PdMonomerCollect> implements IDgdocMonomerService {

    private final DgdocApiClient dgDocApiClient;
    private final IDgdocProjectService projectService;
    private final DgdocMonomerEntMapper dgdocMonomerEntMapper;
    private final DgdocProjectEntMapper projectEntMapper;
    private final DgdocProjectMapper projectMapper;
    private final IDgdocArchivesService archivesService;
    private final IDgdocArchivesInstanceService archivesInstanceService;
    private final DgzjjzjJcjgCoreSampleMapper dgzjjzjJcjgCoreSampleMapper;
    private final PlatformTransactionManager platformTransactionManager;
    private final DgdocMonomerMapper monomerMapper;


    @Override
    public List<DgzjjzjJcjgCoreSampleTableVo> queryReportById(String monomerId) {
        List<DgzjjzjJcjgCoreSampleTableVo> dgzjjzjJcjgCoreSampleTableVos = dgzjjzjJcjgCoreSampleMapper.queryMonomerJcjgList(monomerId);
        for (DgzjjzjJcjgCoreSampleTableVo dgzjjzjJcjgCoreSampleTableVo : dgzjjzjJcjgCoreSampleTableVos) {
            if (StringUtils.isNotBlank(dgzjjzjJcjgCoreSampleTableVo.getSynum())) {
                //查询不合格销案情况
                List<DgzjjzjSupJcjgUnqualifiedReplyFileVo> dgzjjzjSupJcjgUnqualifiedReplyFileVos = dgzjjzjJcjgCoreSampleMapper.queryMonomerJcjgByJcbh(dgzjjzjJcjgCoreSampleTableVo.getSynum());
                dgzjjzjJcjgCoreSampleTableVo.setSupDealProcess(dgzjjzjSupJcjgUnqualifiedReplyFileVos);
            }
        }
        return dgzjjzjJcjgCoreSampleTableVos;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void dataSync() {
        //查询非市政项目的单体插入数据
        List<PdMonomerCollect> pdMonomerCollects = monomerMapper.selectNoSz();
        if (pdMonomerCollects.isEmpty()) {
            return;
        }

        List<DgdocArchives> monomerArchives =
                archivesService.selectArchivesListByTypeAndIsSz(ArchivesType.MONOMER.getCode(), false);

        if (monomerArchives.isEmpty()) {
            return;
        }

        List<DgdocArchivesInstance> allArchivesInstances = pdMonomerCollects.stream()
                .flatMap(pdMonomerCollect -> monomerArchives.stream().map(archive -> {
                    DgdocArchivesInstance instance = BeanUtil.copyProperties(archive, DgdocArchivesInstance.class);
                    // 设置单体档案
                    instance.setArchivesType(ArchivesType.MONOMER.getCode());
                    // 设置单体ID
                    instance.setProjectMonomerId(pdMonomerCollect.getMonomerId());
                    return instance;
                }))
                .collect(Collectors.toList());

        if (!allArchivesInstances.isEmpty()) {
            int batchSize = 1000;
            for (int i = 0; i < allArchivesInstances.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, allArchivesInstances.size());
                List<DgdocArchivesInstance> batch = allArchivesInstances.subList(i, endIndex);
                archivesInstanceService.saveBatch(batch, batchSize);
            }
        }
    }

    /**
     * 高性能版本的数据同步方法 - 适用于超大数据量场景
     * 如果数据量非常大（比如几十万条），建议使用这个方法
     */
    @Transactional(rollbackFor = Exception.class)
    public void dataSyncHighPerformance() {
        //查询非市政项目的单体插入数据
        List<PdMonomerCollect> pdMonomerCollects = monomerMapper.selectNoSz();
        if (pdMonomerCollects.isEmpty()) {
            return;
        }

        // 只查询一次档案模板数据
        List<DgdocArchives> monomerArchives =
                archivesService.selectArchivesListByTypeAndIsSz(ArchivesType.MONOMER.getCode(), false);

        if (monomerArchives.isEmpty()) {
            return;
        }

        // 分批处理单体数据，避免内存溢出
        int monomerBatchSize = 100; // 每批处理100个单体
        for (int i = 0; i < pdMonomerCollects.size(); i += monomerBatchSize) {
            int endIndex = Math.min(i + monomerBatchSize, pdMonomerCollects.size());
            List<PdMonomerCollect> monomerBatch = pdMonomerCollects.subList(i, endIndex);

            // 为当前批次的单体构建档案实例
            List<DgdocArchivesInstance> batchArchivesInstances = monomerBatch.stream()
                    .flatMap(pdMonomerCollect -> {
                        return monomerArchives.stream().map(archive -> {
                            DgdocArchivesInstance instance = BeanUtil.copyProperties(archive, DgdocArchivesInstance.class);
                            instance.setArchivesType(ArchivesType.MONOMER.getCode());
                            instance.setProjectMonomerId(pdMonomerCollect.getMonomerId());
                            return instance;
                        });
                    })
                    .collect(Collectors.toList());

            // 批量插入当前批次的数据
            if (!batchArchivesInstances.isEmpty()) {
                archivesInstanceService.saveBatch(batchArchivesInstances, 1000);
                System.out.println("数据插入成功.....");
            }
        }
    }

    /**
     * 生成SQL文件进行批量插入 - 最高性能方案
     * 直接生成INSERT语句，可以通过数据库客户端或命令行执行
     */
    @Override
    public void generateSqlFile() {
        try {
            //查询非市政项目的单体插入数据
            List<PdMonomerCollect> pdMonomerCollects = monomerMapper.selectNoSz();
            if (pdMonomerCollects.isEmpty()) {
                System.out.println("没有需要同步的单体数据");
                return;
            }

            // 只查询一次档案模板数据
            List<DgdocArchives> monomerArchives =
                    archivesService.selectArchivesListByTypeAndIsSz(ArchivesType.MONOMER.getCode(), false);

            if (monomerArchives.isEmpty()) {
                System.out.println("没有找到档案模板数据");
                return;
            }

            // 生成SQL文件
            String fileName = "batch_insert_archives_" + new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date()) + ".sql";
            FileWriter writer = new FileWriter(fileName);

            // 写入文件头注释
            writer.write("-- 批量插入档案实例数据\n");
            writer.write("-- 生成时间: " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "\n");
            writer.write("-- 单体数量: " + pdMonomerCollects.size() + "\n");
            writer.write("-- 档案模板数量: " + monomerArchives.size() + "\n");
            writer.write("-- 预计插入记录数: " + (pdMonomerCollects.size() * monomerArchives.size()) + "\n\n");

            // 禁用自动提交和外键检查，提高插入性能
            writer.write("SET autocommit = 0;\n");
            writer.write("SET foreign_key_checks = 0;\n");
            writer.write("SET unique_checks = 0;\n");
            writer.write("START TRANSACTION;\n\n");

            int batchSize = 1000; // 每1000条生成一个INSERT语句
            int currentBatch = 0;
            int totalRecords = 0;
            StringBuilder sqlBuilder = new StringBuilder();

            System.out.println("开始生成SQL语句...");

            for (int i = 0; i < pdMonomerCollects.size(); i++) {
                PdMonomerCollect pdMonomerCollect = pdMonomerCollects.get(i);

                for (int j = 0; j < monomerArchives.size(); j++) {
                    DgdocArchives archive = monomerArchives.get(j);

                    if (currentBatch == 0) {
                        // 开始新的INSERT语句
                        sqlBuilder.append("INSERT INTO dgdoc_archives_instance (");
                        sqlBuilder.append("archives_instance_id, archives_id, parent_id, archives_no, archives_name, ");
                        sqlBuilder.append("archives_type, project_monomer_id, upload_state, audit_state, status, ");
                        sqlBuilder.append("create_time, update_time");
                        sqlBuilder.append(") VALUES \n");
                    }

                    if (currentBatch > 0) {
                        sqlBuilder.append(",\n");
                    }

                    // 生成VALUES部分，处理特殊字符
                    String archivesName = archive.getArchivesName() != null ?
                            archive.getArchivesName().replace("'", "''").replace("\\", "\\\\") : "";

                    // 处理可能为null的字段
                    String parentId = archive.getParentId() != null ? archive.getParentId() : "";
                    String archivesNo = archive.getArchivesNo() != null ? archive.getArchivesNo() : "";

                    sqlBuilder.append("('")
                            .append(IdWorker.getId()).append("', '")  // archives_instance_id
                            .append(archive.getArchivesId()).append("', '")      // archives_id
                            .append(parentId).append("', '")            // parent_id
                            .append(archivesNo).append("', '")          // archives_no
                            .append(archivesName).append("', '")        // archives_name
                            .append(ArchivesType.MONOMER.getCode()).append("', '")  // archives_type
                            .append(pdMonomerCollect.getMonomerId()).append("', '") // project_monomer_id
                            .append("0', '")   // upload_state 默认为0-未上传
                            .append("0', '")   // audit_state 默认为0-未归档
                            .append("0', ")    // status 默认为0-正常
                            .append("NOW(), NOW()")  // create_time, update_time
                            .append(")");

                    currentBatch++;
                    totalRecords++;

                    // 达到批次大小或者是最后一条记录
                    boolean isLastRecord = (i == pdMonomerCollects.size() - 1 && j == monomerArchives.size() - 1);
                    if (currentBatch >= batchSize || isLastRecord) {
                        sqlBuilder.append(";\n\n");
                        writer.write(sqlBuilder.toString());
                        sqlBuilder.setLength(0);
                        currentBatch = 0;

                        // 显示进度
                        if (totalRecords % 10000 == 0 || isLastRecord) {
                            System.out.println("已生成 " + totalRecords + " 条记录的SQL语句");
                        }
                    }
                }
            }

            // 恢复设置并提交事务
            writer.write("COMMIT;\n");
            writer.write("SET foreign_key_checks = 1;\n");
            writer.write("SET unique_checks = 1;\n");
            writer.write("SET autocommit = 1;\n");
            writer.close();

            System.out.println("SQL文件生成成功: " + fileName);
            System.out.println("总记录数: " + totalRecords);
            System.out.println("文件大小: " + new java.io.File(fileName).length() / 1024 + " KB");
            System.out.println("\n执行方式:");
            System.out.println("1. 命令行执行: mysql -u用户名 -p密码 数据库名 < " + fileName);
            System.out.println("2. 或在MySQL客户端中执行: source " + fileName);

        } catch (IOException e) {
            System.err.println("生成SQL文件失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    @Override
    public List<PdMonomerCollectTableVo> queryList(String projectId) {
        Assert.notNull(projectId, "项目ID不能为空");

        LambdaQueryWrapper<PdMonomerCollect> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PdMonomerCollect::getProjectId, projectId);
        queryWrapper.orderByDesc(PdMonomerCollect::getCreateTime);

        List<PdMonomerCollect> monomerList = baseMapper.selectList(queryWrapper);
        List<PdMonomerCollectTableVo> pdMonomerCollects = BeanUtil.copyToList(monomerList, PdMonomerCollectTableVo.class);


        for (PdMonomerCollectTableVo pdMonomerCollect : pdMonomerCollects) {
            //查询单体参建单位信息
            List<PdMonomerEntCollect> pdMonomerEntCollects = dgdocMonomerEntMapper.selectList(Wrappers.<PdMonomerEntCollect>lambdaQuery().eq(PdMonomerEntCollect::getMonomerId, pdMonomerCollect.getMonomerId()));

            Map<String, PdMonomerEntCollect> entCollectMap = pdMonomerEntCollects.stream().collect(Collectors.toMap(PdMonomerEntCollect::getEntType, Function.identity()));

            pdMonomerCollect.setSgUnit(entCollectMap.get(EntType.CONSTRUCTION.getCode()));
            pdMonomerCollect.setSjUnit(entCollectMap.get(EntType.DESIGN.getCode()));
            pdMonomerCollect.setJlUnit(entCollectMap.get(EntType.SUPERVISION.getCode()));
            pdMonomerCollect.setKcUnit(entCollectMap.get(EntType.SURVEY.getCode()));

            //查询项目名称
            PdProjectCollect project = projectService.getById(pdMonomerCollect.getProjectId());
            pdMonomerCollect.setProjectName(project.getProjectName());
        }
        return pdMonomerCollects;
    }

    @Override
    public MonomerVo queryById(String monomerId) {
        MonomerVo monomerVo = new MonomerVo();
        PdMonomerCollect monomerCollect = this.getById(monomerId);
        Assert.notNull(monomerCollect, "查询不到单体信息");

        BeanUtil.copyProperties(monomerCollect, monomerVo);

        //查询单体参建单位信息
        List<PdMonomerEntCollect> pdMonomerEntCollects = dgdocMonomerEntMapper.selectList(Wrappers.<PdMonomerEntCollect>lambdaQuery().eq(PdMonomerEntCollect::getMonomerId, monomerId));

        Map<String, PdMonomerEntCollect> entCollectMap = pdMonomerEntCollects.stream().collect(Collectors.toMap(PdMonomerEntCollect::getEntType, Function.identity()));

        monomerVo.setSgUnit(entCollectMap.get(EntType.CONSTRUCTION.getCode()));
        monomerVo.setSjUnit(entCollectMap.get(EntType.DESIGN.getCode()));
        monomerVo.setJlUnit(entCollectMap.get(EntType.SUPERVISION.getCode()));
        monomerVo.setKcUnit(entCollectMap.get(EntType.SURVEY.getCode()));

        //查询项目名称
        PdProjectCollect project = projectService.getById(monomerCollect.getProjectId());
        monomerVo.setProjectName(project.getProjectName());


        return monomerVo;
    }

    @Override
    public Result<Rs<PdMonomerCollect>> queryMonomerList(MonomerQuery query) {
        PdMonomerCollect monomerCollect = BeanUtil.toBean(query, PdMonomerCollect.class);
        Result<Rs<PdMonomerCollect>> result = dgDocApiClient.getMonomerCollect(monomerCollect);
        PdProjectCollect project = projectService.getById(query.getProjectId());
        result.getResult().getList().forEach(a -> {
            //设置项目名称
            a.setProjectName(project.getProjectName());

            //不需要施工表查询了，在查单体时已完善以下信息 20210320 by lixh
//            if (StringUtils.isNotBlank(a.getDataId())) {
//                //查询施工许可证信息
//                PdConsPermitCollect pdConsPermitCollect = new PdConsPermitCollect();
//                pdConsPermitCollect.setConsPermitId(a.getDataId());
//
//                Result<Rs<PdConsPermitCollect>> permitCollect = dgDocApiClient.getConsPermitCollect(pdConsPermitCollect);
//                if (permitCollect.isSuccess() && !permitCollect.getResult().getList().isEmpty()) {
//                    PdConsPermitCollect consPermitCollect = permitCollect.getResult().getList().get(0);
//                    //计划开始时间
//                    a.setPlanStartDate(consPermitCollect.getStartdate());
//                    //计划结束时间
//                    a.setPlanEndDate(consPermitCollect.getEnddate());
//                    //施工许可证url
//                    a.setLicenceFile(consPermitCollect.getConsFileUrl());
//                    //建设工程规划许可证证号
//                    a.setGcghxkNo(consPermitCollect.getGcghxkNo());
//                }
//            }

        });
        List<PdMonomerCollect> pdMonomerCollects = result.getResult().getList();
        //如果是房地产项目，且单体信息为空，则构建虚拟单体信息
        if (project.getIsSz() && pdMonomerCollects.isEmpty()) {
            PdMonomerCollect monomer = BeanUtil.toBean(monomerCollect, PdMonomerCollect.class);
            monomer.setProjectId(query.getProjectId());
            monomer.setProjectName(project.getProjectName());
            monomer.setMonomerName(project.getProjectName());
            monomer.setMonomerId(UUIDGenerator.generate());
            monomer.setDataSource(MonomerDataSource.GENERATED_BY_PROJECT.getCode());
            monomer.setIsVirtual(true);
            pdMonomerCollects.add(monomer);
            return result;
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean remove(List<String> monomerIds) {
        List<PdMonomerCollect> monomerCollects = baseMapper.selectBatchIds(monomerIds);

        for (PdMonomerCollect monomerCollect : monomerCollects) {
            //查询单体档案信息
            List<DgdocArchivesInstance> archivesInstances = archivesInstanceService.list(Wrappers.<DgdocArchivesInstance>lambdaQuery()
                    .eq(DgdocArchivesInstance::getProjectMonomerId, monomerCollect.getMonomerId()));

            //如果存在上传的单体档案信息，则不允许删除
            boolean allMatch = archivesInstances.stream().allMatch(a -> a.getUploadState().equals(UploadState.UNUPLOAD.getCode()));
            if (!allMatch) {
                throw new JeecgBootException("单体下存在已上传的档案信息，不允许删除");
            }
            //删除单体档案
            archivesInstanceService.removeByIds(archivesInstances);
            //删除单体参建单位信息
            List<PdMonomerEntCollect> pdMonomerEntCollects = dgdocMonomerEntMapper.selectList(Wrappers.<PdMonomerEntCollect>lambdaQuery().eq(PdMonomerEntCollect::getMonomerId, monomerCollect.getMonomerId()));

            if (!pdMonomerEntCollects.isEmpty()) {
                List<String> entIds = pdMonomerEntCollects.stream().map(PdMonomerEntCollect::getEntId).collect(Collectors.toList());
                //删除项目参建单位信息
                projectEntMapper.delete(Wrappers.<PdProjectEntCollect>lambdaQuery().in(PdProjectEntCollect::getEntId, entIds));
                //删除单体参建单位信息
                dgdocMonomerEntMapper.delete(Wrappers.<PdMonomerEntCollect>lambdaQuery().in(PdMonomerEntCollect::getEntId, entIds).eq(PdMonomerEntCollect::getMonomerId, monomerCollect.getMonomerId()));
            }

        }

        return this.removeByIds(new ArrayList<>(monomerIds));
    }

    @SneakyThrows
    @Override
    @Transactional
    public Boolean update(MonomerBo bo) {
        PdMonomerCollect pdMonomerCollect = BeanUtil.toBean(bo, PdMonomerCollect.class);
        saveBeforeValidated(bo);
        //保存单体参建单位信息
        this.saveMonomerUnitInfo(bo);
        return this.updateById(pdMonomerCollect);
    }

    @Override
    public Result<Rs<PdMonomerEntCollect>> queryEntList(MonomerEntQuery query) {
        PdMonomerEntCollect monomerEntCollect = BeanUtil.toBean(query, PdMonomerEntCollect.class);

        PdProjectCollect pdProjectCollect = projectMapper.selectById(query.getProjectId());
        Result<Rs<PdMonomerEntCollect>> result = dgDocApiClient.getMonomerEntCollect(monomerEntCollect);
        result.getResult().getList().forEach(a -> {
            if (StringUtils.isBlank(a.getEntId())) {
                //查询参建单位信息
                PdEntInfoCollect pdEntInfoCollect = new PdEntInfoCollect();
                pdEntInfoCollect.setEntType(a.getEntType());
                pdEntInfoCollect.setEntName(a.getEntName());
                Result<Rs<PdEntInfoCollect>> entCollect = dgDocApiClient.getEntCollect(pdEntInfoCollect);
                if (entCollect.isSuccess() && !entCollect.getResult().getList().isEmpty()) {
                    a.setEntId(entCollect.getResult().getList().get(0).getEntId());
                }
            }
        });
        if (pdProjectCollect.getIsSz() && result.getResult().getList().isEmpty()) {
            //查询项目的参建单位信息
            List<PdProjectEntCollect> pdProjectEntCollects = projectEntMapper.selectList(Wrappers.<PdProjectEntCollect>lambdaQuery()
                    .eq(PdProjectEntCollect::getProjectId, query.getProjectId())
                    .eq(PdProjectEntCollect::getEntType, query.getEntType()));

            if (pdProjectEntCollects.isEmpty()) {
                return result;
            }
            List<PdMonomerEntCollect> pdMonomerEntCollects = BeanUtil.copyToList(pdProjectEntCollects, PdMonomerEntCollect.class);
            //设置单体ID
            pdMonomerEntCollects.forEach(a -> a.setMonomerId(query.getMonomerId()));

            Result<Rs<PdMonomerEntCollect>> rsResult = new Result<>();
            Rs<PdMonomerEntCollect> rs = new Rs<>();
            rs.setList(pdMonomerEntCollects);
            rsResult.setResult(rs);
            return rsResult;
        }
        List<PdMonomerEntCollect> filterList = result.getResult().getList().stream().filter(a -> StringUtils.isNotBlank(a.getEntId())).collect(Collectors.toList());
        result.getResult().setList(filterList);
        return result;
    }

    @Override
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public Boolean insert(MonomerBo bo) {
        PdMonomerCollect pdMonomerCollect = BeanUtil.toBean(bo, PdMonomerCollect.class);

        boolean exists = baseMapper.exists(Wrappers.<PdMonomerCollect>lambdaQuery().eq(PdMonomerCollect::getMonomerId, pdMonomerCollect.getMonomerId()));
        if (exists) {
            throw new EstarException("单体已存在");
        }

        saveBeforeValidated(bo);


        boolean save = this.save(pdMonomerCollect);
        if (save) {
            //保存单体参建单位信息
            this.saveMonomerUnitInfo(bo);
            saveArchivesInstance(pdMonomerCollect);
        }
        return save;
    }

    /**
     * 保存档案实例
     *
     * @param pdMonomerCollect
     */
    @Async("asyncTaskExecutor")
    public void saveArchivesInstance(PdMonomerCollect pdMonomerCollect) {
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        TransactionStatus transaction = platformTransactionManager.getTransaction(def);
        try {
            PdProjectCollect project = projectMapper.selectById(pdMonomerCollect.getProjectId());

            //保存单体档案
            List<DgdocArchives> monomerArchives;
            if (project.getIsSz()) {
                monomerArchives =
                        archivesService.selectArchivesListByTypeAndIsSz(ArchivesType.MONOMER.getCode(), true);
            } else {
                monomerArchives =
                        archivesService.selectArchivesListByTypeAndIsSz(ArchivesType.MONOMER.getCode(), false);
            }
            List<DgdocArchivesInstance> archivesInstances = BeanUtil.copyToList(monomerArchives, DgdocArchivesInstance.class);
            archivesInstances.forEach(a -> {
                //设置单体档案
                a.setArchivesType(ArchivesType.MONOMER.getCode());
                //设置单体ID
                a.setProjectMonomerId(pdMonomerCollect.getMonomerId());
            });
            archivesInstanceService.saveBatch(archivesInstances);

            //设置单体项目档案
            List<DgdocArchives> projectArchives;
            if (project.getIsSz()) {
                projectArchives =
                        archivesService.selectArchivesListByTypeAndIsSz(ArchivesType.PROJECT.getCode(), true);
            } else {
                projectArchives =
                        archivesService.selectArchivesListByTypeAndIsSz(ArchivesType.PROJECT.getCode(), false);
            }
            List<DgdocArchivesInstance> projectArchivesInstances = BeanUtil.copyToList(projectArchives, DgdocArchivesInstance.class);
            projectArchivesInstances.forEach(a -> {
                //设置项目档案
                a.setArchivesType(ArchivesType.PROJECT.getCode());
                //设置项目ID
                a.setProjectMonomerId(pdMonomerCollect.getMonomerId());
            });
            archivesInstanceService.saveBatch(projectArchivesInstances);

            platformTransactionManager.commit(transaction);
        } catch (Exception e) {
            platformTransactionManager.rollback(transaction);
            throw e;
        }

    }


    private static void saveBeforeValidated(MonomerBo bo) {
        //结束时间不能大于开始时间
        if (bo.getPlanStartDate() != null && bo.getPlanEndDate() != null && bo.getPlanEndDate().getTime() < bo.getPlanStartDate().getTime()) {
            throw new EstarException("计划完成时间不能大于计划开始时间");
        }
    }

    public void saveMonomerUnitInfo(MonomerBo bo) throws Exception {
        //删除单体参建单位信息
        dgdocMonomerEntMapper.delete(Wrappers.<PdMonomerEntCollect>lambdaQuery().eq(PdMonomerEntCollect::getMonomerId, bo.getMonomerId()));

        if (bo.getSgUnit() != null) {
            boolean sgUnitEx = dgdocMonomerEntMapper.exists(Wrappers.<PdMonomerEntCollect>lambdaQuery()
                    .eq(PdMonomerEntCollect::getProjectId, bo.getProjectId())
                    .eq(PdMonomerEntCollect::getMonomerId, bo.getMonomerId())
                    .eq(PdMonomerEntCollect::getEntType, EntType.CONSTRUCTION.getCode()));
            if (!sgUnitEx) {
                dgdocMonomerEntMapper.insert(bo.getSgUnit());
            }
            //查询关联项目的参建单位是否存在，不存在则新增
            boolean exists = projectEntMapper.exists(Wrappers.<PdProjectEntCollect>lambdaQuery().eq(PdProjectEntCollect::getProjectId, bo.getProjectId())
                    .eq(PdProjectEntCollect::getEntId, bo.getSgUnit().getEntId()));
            if (!exists) {
                PdProjectEntCollect projectEntCollect = BeanUtil.toBean(bo.getSgUnit(), PdProjectEntCollect.class);
                projectEntMapper.insert(projectEntCollect);
            }
            //添加用户
            this.addUser(bo.getSgUnit());
        }
        if (bo.getSjUnit() != null) {
            boolean sgUnitSj = dgdocMonomerEntMapper.exists(Wrappers.<PdMonomerEntCollect>lambdaQuery()
                    .eq(PdMonomerEntCollect::getProjectId, bo.getProjectId())
                    .eq(PdMonomerEntCollect::getMonomerId, bo.getMonomerId())
                    .eq(PdMonomerEntCollect::getEntType, EntType.DESIGN.getCode()));
            if (!sgUnitSj) {
                dgdocMonomerEntMapper.insert(bo.getSjUnit());
            }
            //查询关联项目的参建单位是否存在，不存在则新增
            boolean exists = projectEntMapper.exists(Wrappers.<PdProjectEntCollect>lambdaQuery().eq(PdProjectEntCollect::getProjectId, bo.getProjectId())
                    .eq(PdProjectEntCollect::getEntId, bo.getSjUnit().getEntId()));
            if (!exists) {
                PdProjectEntCollect projectEntCollect = BeanUtil.toBean(bo.getSjUnit(), PdProjectEntCollect.class);
                projectEntMapper.insert(projectEntCollect);
            }
            //添加用户
            this.addUser(bo.getSjUnit());
        }
        if (bo.getJlUnit() != null) {
            boolean sgUnitJl = dgdocMonomerEntMapper.exists(Wrappers.<PdMonomerEntCollect>lambdaQuery()
                    .eq(PdMonomerEntCollect::getProjectId, bo.getProjectId())
                    .eq(PdMonomerEntCollect::getMonomerId, bo.getMonomerId())
                    .eq(PdMonomerEntCollect::getEntType, EntType.SUPERVISION.getCode()));
            if (!sgUnitJl) {
                dgdocMonomerEntMapper.insert(bo.getJlUnit());
            }
            //查询关联项目的参建单位是否存在，不存在则新增
            boolean exists = projectEntMapper.exists(Wrappers.<PdProjectEntCollect>lambdaQuery().eq(PdProjectEntCollect::getProjectId, bo.getProjectId())
                    .eq(PdProjectEntCollect::getEntId, bo.getJlUnit().getEntId()));
            if (!exists) {
                PdProjectEntCollect projectEntCollect = BeanUtil.toBean(bo.getJlUnit(), PdProjectEntCollect.class);
                projectEntMapper.insert(projectEntCollect);
            }
            //添加用户
            this.addUser(bo.getJlUnit());
        }
        if (bo.getKcUnit() != null) {
            boolean sgUnitKc = dgdocMonomerEntMapper.exists(Wrappers.<PdMonomerEntCollect>lambdaQuery()
                    .eq(PdMonomerEntCollect::getProjectId, bo.getProjectId())
                    .eq(PdMonomerEntCollect::getMonomerId, bo.getMonomerId())
                    .eq(PdMonomerEntCollect::getEntType, EntType.SURVEY.getCode()));
            if (!sgUnitKc) {
                dgdocMonomerEntMapper.insert(bo.getKcUnit());
            }
            //查询关联项目的参建单位是否存在，不存在则新增
            boolean exists = projectEntMapper.exists(Wrappers.<PdProjectEntCollect>lambdaQuery().eq(PdProjectEntCollect::getProjectId, bo.getProjectId())
                    .eq(PdProjectEntCollect::getEntId, bo.getKcUnit().getEntId()));
            if (!exists) {
                PdProjectEntCollect projectEntCollect = BeanUtil.toBean(bo.getKcUnit(), PdProjectEntCollect.class);
                projectEntMapper.insert(projectEntCollect);
            }
            //添加用户
            this.addUser(bo.getKcUnit());
        }
    }

    /**
     * 添加用户
     *
     * @param bo
     */
    public void addUser(PdMonomerEntCollect bo) throws Exception {
        if (StringUtils.isBlank(bo.getEntId())) {
            return;
        }
        //添加用户信息
        SysUserDto sysUser = new SysUserDto();
        sysUser.setRealname(bo.getEntName());
        sysUser.setOrganizationId(bo.getCreditCode());
        sysUser.setEntType(bo.getEntType());
        Result<String> rs = dgDocApiClient.addDocEnt(sysUser);
        if (!rs.isSuccess()) {
            throw new EstarException("添加用户失败");
        }

    }
}
