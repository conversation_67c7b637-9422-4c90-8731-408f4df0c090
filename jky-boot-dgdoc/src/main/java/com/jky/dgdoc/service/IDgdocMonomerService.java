package com.jky.dgdoc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jky.dgdoc.domain.PdMonomerCollect;
import com.jky.dgdoc.domain.PdMonomerEntCollect;
import com.jky.dgdoc.domain.Rs;
import com.jky.dgdoc.domain.bo.MonomerBo;
import com.jky.dgdoc.domain.query.MonomerEntQuery;
import com.jky.dgdoc.domain.query.MonomerQuery;
import com.jky.dgdoc.domain.vo.DgzjjzjJcjgCoreSampleTableVo;
import com.jky.dgdoc.domain.vo.MonomerVo;
import com.jky.dgdoc.domain.vo.PdMonomerCollectTableVo;
import org.jeecg.common.api.vo.Result;

import java.util.List;

/**
 * @Author: lpg
 * @Date: 2024/01/02/16:48
 * @Description:
 */
public interface IDgdocMonomerService extends IService<PdMonomerCollect> {
    /**
     * 新增单体
     *
     * @param bo
     * @return
     */
    Boolean insert(MonomerBo bo);

    /**
     * 查询单体关联单位信息
     *
     * @param query
     * @return
     */
    Result<Rs<PdMonomerEntCollect>> queryEntList(MonomerEntQuery query);

    /**
     * 修改单体项目
     *
     * @param bo
     * @return
     */
    Boolean update(MonomerBo bo);

    /**
     * 删除单体
     *
     * @param monomerIds
     * @return
     */
    Boolean remove(List<String> monomerIds);

    /**
     * 查询单体信息
     *
     * @param query
     * @return
     */
    Result<Rs<PdMonomerCollect>> queryMonomerList(MonomerQuery query);

    /**
     * 查询单体详细信息
     *
     * @param monomerId
     * @return
     */
    MonomerVo queryById(String monomerId);

    /**
     * 查询单体列表
     *
     * @param projectId
     * @return
     */
    List<PdMonomerCollectTableVo> queryList(String projectId);

    /**
     * 查询单体检测报告
     *
     * @param monomerId
     * @return
     */
    List<DgzjjzjJcjgCoreSampleTableVo> queryReportById(String monomerId);

    void dataSync();
}
