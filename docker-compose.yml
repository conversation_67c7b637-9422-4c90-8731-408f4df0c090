version: '2'
services:
  jky-boot-mysql:
    build:
      context: ./db
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_ROOT_HOST: '%'
      TZ: Asia/Shanghai
    restart: always
    container_name: jky-boot-mysql
    image: jky-boot-mysql
    command:
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_general_ci
      --explicit_defaults_for_timestamp=true
      --lower_case_table_names=1
      --max_allowed_packet=128M
      --default-authentication-plugin=caching_sha2_password
    ports:
      - 3306:3306

  jky-boot-redis:
    image: redis:5.0
    ports:
      - 6379:6379
    restart: always
    hostname: jky-boot-redis
    container_name: jky-boot-redis

  jky-boot-system:
    build:
      context: ./jky-boot-module-system
    restart: on-failure
    depends_on:
      - jky-boot-mysql
      - jky-boot-redis
    container_name: jky-boot-system
    image: jky-boot-system
    hostname: jky-boot-system
    ports:
      - 8080:8080